@echo off
setlocal

echo ========================================
echo   HVNC System Test Suite
echo ========================================
echo.

REM Check if executables exist
if not exist "build\hvnc_viewer.exe" (
    echo [ERROR] HVNC executables not found. Please run build.bat first.
    pause
    exit /b 1
)

echo Available test modes:
echo   1. Basic Screenshot Transfer Test
echo   2. Hidden VNC System Test  
echo   3. Enhanced HVNC with Network Resilience Test
echo   4. Performance Benchmark Test
echo   5. All Tests (Sequential)
echo.

set /p choice="Select test mode (1-5): "

if "%choice%"=="1" goto test_basic
if "%choice%"=="2" goto test_hvnc
if "%choice%"=="3" goto test_enhanced
if "%choice%"=="4" goto test_performance
if "%choice%"=="5" goto test_all
goto invalid_choice

:test_basic
echo.
echo ========================================
echo   Basic Screenshot Transfer Test
echo ========================================
echo.
echo Starting basic server...
start /B "Basic Server" build\server.exe

timeout /t 2 /nobreak >nul

echo Sending test screenshot...
build\simple_client.exe

echo.
echo Test completed! Check screenshots folder.
echo Stopping server...
taskkill /F /IM server.exe >nul 2>&1
goto end

:test_hvnc
echo.
echo ========================================
echo   Hidden VNC System Test
echo ========================================
echo.
echo Starting HVNC Viewer (GUI will open)...
start "HVNC Viewer" build\hvnc_viewer.exe

timeout /t 3 /nobreak >nul

echo Starting HVNC Client (hidden desktop capture)...
echo This will capture from a hidden desktop for 30 seconds...
timeout /t 30 build\hvnc_client.exe --interval 2

echo.
echo Test completed! Check the HVNC Viewer window.
echo Close the viewer window manually.
goto end

:test_enhanced
echo.
echo ========================================
echo   Enhanced HVNC Test
echo ========================================
echo.
echo Starting HVNC Viewer...
start "HVNC Viewer" build\hvnc_viewer.exe

timeout /t 3 /nobreak >nul

echo Starting Enhanced HVNC Client with high resolution...
echo This will run for 60 seconds with network resilience features...
timeout /t 60 build\hvnc_client_enhanced.exe --interval 1 --resolution 1920x1080 --heartbeat 3

echo.
echo Enhanced test completed!
echo Close the viewer window manually.
goto end

:test_performance
echo.
echo ========================================
echo   Performance Benchmark Test
echo ========================================
echo.
echo Starting HVNC Viewer...
start "HVNC Viewer" build\hvnc_viewer.exe

timeout /t 3 /nobreak >nul

echo Running performance test (120 seconds)...
echo Testing different capture intervals and resolutions...

echo.
echo Phase 1: High frequency, low resolution (30s)
timeout /t 30 build\hvnc_client_enhanced.exe --interval 0.5 --resolution 800x600

echo.
echo Phase 2: Medium frequency, medium resolution (30s)  
timeout /t 30 build\hvnc_client_enhanced.exe --interval 1 --resolution 1024x768

echo.
echo Phase 3: Low frequency, high resolution (30s)
timeout /t 30 build\hvnc_client_enhanced.exe --interval 2 --resolution 1920x1080

echo.
echo Phase 4: Ultra-high frequency test (30s)
timeout /t 30 build\hvnc_client_enhanced.exe --interval 0.2 --resolution 640x480

echo.
echo Performance benchmark completed!
echo Check the viewer for statistics and performance metrics.
goto end

:test_all
echo.
echo ========================================
echo   Running All Tests Sequentially
echo ========================================
echo.

echo [1/4] Basic Screenshot Transfer Test...
call :test_basic_silent

echo.
echo [2/4] Hidden VNC System Test...
call :test_hvnc_silent

echo.
echo [3/4] Enhanced HVNC Test...
call :test_enhanced_silent

echo.
echo [4/4] Performance Benchmark...
call :test_performance_silent

echo.
echo ========================================
echo   All Tests Completed!
echo ========================================
goto end

:test_basic_silent
start /B "Basic Server" build\server.exe
timeout /t 2 /nobreak >nul
build\simple_client.exe >nul
taskkill /F /IM server.exe >nul 2>&1
echo Basic test completed.
goto :eof

:test_hvnc_silent
start "HVNC Viewer" build\hvnc_viewer.exe
timeout /t 3 /nobreak >nul
timeout /t 15 build\hvnc_client.exe --interval 3 >nul
echo HVNC test completed.
goto :eof

:test_enhanced_silent
timeout /t 30 build\hvnc_client_enhanced.exe --interval 2 --resolution 1024x768 >nul
echo Enhanced test completed.
goto :eof

:test_performance_silent
timeout /t 60 build\hvnc_client_enhanced.exe --interval 1 --resolution 1280x720 >nul
echo Performance test completed.
goto :eof

:invalid_choice
echo.
echo [ERROR] Invalid choice. Please select 1-5.
pause
exit /b 1

:end
echo.
echo ========================================
echo   Test Summary
echo ========================================
echo.
echo Files created:
if exist "screenshots" (
    echo Screenshots folder:
    dir /b screenshots 2>nul | findstr /r ".*" >nul && (
        for %%f in (screenshots\*) do echo   - %%f
    ) || echo   (No files)
)

if exist "test_screenshot.bmp" (
    echo   - test_screenshot.bmp (local test file)
)

if exist "capture_*.bmp" (
    echo Captured frames:
    for %%f in (capture_*.bmp) do echo   - %%f
)

echo.
echo Verification:
if exist "build\verify_bmp.exe" (
    if exist "test_screenshot.bmp" (
        echo Verifying test screenshot...
        build\verify_bmp.exe test_screenshot.bmp
    )
) else (
    echo verify_bmp.exe not available
)

echo.
echo ========================================
echo   HVNC System Test Complete!
echo ========================================
echo.
echo Next steps:
echo   - Review captured screenshots
echo   - Check HVNC Viewer statistics
echo   - Test with different network conditions
echo   - Customize capture settings
echo.
echo For production use:
echo   - Configure firewall rules
echo   - Set up authentication (if needed)
echo   - Monitor performance metrics
echo   - Implement logging
echo.

pause
