# 🎯 Clean HVNC System - Hidden Desktop Capture

## ✅ **WORKING AND TESTED!**

A clean, minimal Hidden VNC (HVNC) system that creates a hidden desktop and captures from it, based on your 3-year-old reference implementation.

---

## 🚀 **Quick Start**

### **Basic Hidden VNC**
```bash
# Build everything
.\build.bat

# Terminal 1: Start GUI Viewer
build\hvnc_viewer.exe

# Terminal 2: Start Hidden Desktop Client
build\hvnc_client.exe
```

### **Hidden VNC with Explorer**
```bash
# Terminal 1: Start GUI Viewer
build\hvnc_viewer.exe

# Terminal 2: Start client with Explorer on hidden desktop
build\hvnc_client.exe --start-explorer --resolution 1024x768
```

---

## 🏗️ **System Overview**

### **Core Components**
- ✅ **hvnc_client.exe** - Hidden desktop capture client (283KB)
- ✅ **hvnc_viewer.exe** - Real-time GUI viewer server (285KB)
- ✅ **server.exe** - Basic screenshot receiver (294KB)
- ✅ **client.exe** - Basic screen capture sender (275KB)

### **Key Features**
- **Hidden Desktop Creation** - Creates isolated virtual desktop using Windows CreateDesktop API
- **Explorer Integration** - Can start Windows Explorer on the hidden desktop
- **Real-Time Viewing** - GUI viewer displays captured frames in real-time
- **Clean Implementation** - Minimal, focused codebase based on 3-year-old reference
- **BMP Format** - Reliable, uncompressed image transmission

---

## ⚙️ **Configuration Options**

### **HVNC Client**
```bash
hvnc_client.exe [options]

--ip <address>        Server IP address (default: 127.0.0.1)
--port <number>       Server port (default: 8888)
--interval <seconds>  Capture interval (default: 1)
--resolution <WxH>    Desktop resolution (default: 1024x768)
--desktop-name <name> Hidden desktop name (default: HVNCDesktop)
--start-explorer      Start Explorer on hidden desktop
--help                Show help information
```

---

## 📊 **Test Results**

### **Successful Testing**
- ✅ **Hidden Desktop Creation** - "Hidden desktop 'HVNCDesktop' created"
- ✅ **Explorer Integration** - "Explorer started on hidden desktop"
- ✅ **Network Connection** - "Connected to server"
- ✅ **Frame Transmission** - "Sent frame #30 (2304 KB)"
- ✅ **Real-Time Display** - GUI viewer showing live frames
- ✅ **Clean Disconnection** - Proper cleanup on exit

### **Performance**
- **Frame Size**: ~2.3 MB per frame (1024x768 BMP)
- **Capture Rate**: Configurable (default: 1 second intervals)
- **Memory Usage**: Low system impact
- **Network Transfer**: Reliable TCP transmission

---

## 🔧 **Technical Implementation**

### **Hidden Desktop Technology**
- **CreateDesktop API** - Creates isolated desktop environment
- **SetThreadDesktop** - Switches between desktops for capture
- **Desktop Permissions** - Full access rights for screen capture
- **Explorer Integration** - Automatic Explorer startup on hidden desktop

### **Network Protocol**
- **TCP Communication** - Reliable data transmission
- **Size Prefixing** - 4-byte header for frame size
- **BMP Format** - Raw bitmap data transmission
- **Error Handling** - Connection recovery and retry logic

### **GUI Viewer**
- **Win32 API** - Native Windows interface
- **Real-Time Display** - Timer-based refresh (100ms)
- **Status Monitoring** - Connection status and statistics
- **Frame Saving** - Save current frame as BMP file

---

## 📁 **File Structure**

```
hvnc-system/
├── 📄 hvnc_client.cpp           ✅ Hidden desktop capture client
├── 📄 hvnc_viewer.cpp           ✅ GUI viewer server
├── 📄 server.cpp                ✅ Basic screenshot receiver
├── 📄 client.cpp                ✅ Basic screen capture sender
├── 📄 simple_client.cpp         ✅ Test client with local save
├── 📄 verify_bmp.cpp            ✅ BMP validation tool
├── 📄 stb_image_write.h          ✅ Image compression library
├── 📄 build.bat                 ✅ Build script
├── 📁 build/                    ✅ Compiled executables
│   ├── hvnc_client.exe          ✅ 283,136 bytes - WORKING
│   ├── hvnc_viewer.exe          ✅ 285,184 bytes - WORKING
│   ├── server.exe               ✅ 294,400 bytes
│   ├── client.exe               ✅ 274,944 bytes
│   ├── simple_client.exe        ✅ 277,504 bytes
│   └── verify_bmp.exe           ✅ 276,480 bytes
└── 📄 README.md                 ✅ This documentation
```

---

## 🎯 **Key Achievements**

### **Clean Implementation**
- ✅ **Minimal Codebase** - Focused on core functionality
- ✅ **Based on 3-Year-Old Reference** - Incorporates proven techniques
- ✅ **Hidden Desktop Capture** - Properly captures from virtual desktop
- ✅ **Explorer Integration** - Can start applications on hidden desktop
- ✅ **Real-Time Viewing** - Live display in GUI window

### **Working Features**
- ✅ **Hidden Desktop Creation** - Creates isolated virtual desktop
- ✅ **Screen Capture** - Captures from hidden desktop (not main desktop)
- ✅ **Network Communication** - Reliable TCP transmission
- ✅ **GUI Viewer** - Real-time display with statistics
- ✅ **Application Launching** - Start Explorer on hidden desktop
- ✅ **Clean Shutdown** - Proper resource cleanup

---

## 🔒 **Security Notes**

⚠️ **Important**: This system creates hidden desktops and captures screen content. Use only on systems you own or have explicit permission to monitor.

### **Recommendations**
- Use only for legitimate purposes (testing, monitoring, administration)
- Ensure compliance with local privacy laws
- Consider adding authentication for production use
- Monitor system resources and performance

---

## 🏆 **Success Summary**

**The Clean HVNC System is fully functional and ready for use!**

### **What Works**
- ✅ Hidden desktop creation and management
- ✅ Screen capture from virtual desktop (not main desktop)
- ✅ Real-time GUI viewer with live display
- ✅ Explorer integration on hidden desktop
- ✅ Reliable network communication
- ✅ Clean, minimal implementation

### **Test Output**
```
[+] Hidden desktop 'HVNCDesktop' created
[+] Explorer started on hidden desktop
[+] Connected to server
[*] Sent frame #30 (2304 KB)
```

**The system successfully creates a hidden desktop and captures from it, exactly as requested!** 🎉

---

*Built with C++20, Win32 API, and based on proven 3-year-old reference implementation*
