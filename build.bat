@echo off
echo Building Screenshot Transfer System...

REM Check if Visual Studio is available
where cl >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Visual Studio compiler not found. Please run this from a Visual Studio Developer Command Prompt.
    echo Alternatively, you can use the CMake build method.
    pause
    exit /b 1
)

REM Create build directory
if not exist build mkdir build
cd build

echo.
echo Building server...
cl /std:c++20 /EHsc /Fe:server.exe ..\server.cpp /link Ws2_32.lib User32.lib Gdi32.lib
if %ERRORLEVEL% NEQ 0 (
    echo Failed to build server
    cd ..
    pause
    exit /b 1
)

echo.
echo Building client...
cl /std:c++20 /EHsc /Fe:client.exe ..\client.cpp /link Ws2_32.lib User32.lib Gdi32.lib
if %ERRORLEVEL% NEQ 0 (
    echo Failed to build client
    cd ..
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo.
echo Executables created:
echo   - build\server.exe
echo   - build\client.exe
echo.
echo To test:
echo   1. Run: build\server.exe
echo   2. In another terminal: build\client.exe
echo.

cd ..
pause
