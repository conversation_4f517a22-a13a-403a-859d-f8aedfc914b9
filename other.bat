@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Modern HVNC MSVC Build Script
echo ========================================

:: Check if we're already in a Visual Studio command prompt
where cl >nul 2>&1
if errorlevel 1 (
    echo MSVC compiler already available
    goto :compiler_ready
)

echo Setting up Visual Studio environment...

:: Try to find Visual Studio installations using vswhere (VS 2017+)
set "VSWHERE=%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe"
if exist "%VSWHERE%" (
    echo Using vswhere to locate Visual Studio...
    for /f "usebackq delims=" %%i in (`"%VSWHERE%" -latest -products * -requires Microsoft.VisualStudio.Component.VC.Tools.x86.x64 -property installationPath 2^>nul`) do (
        set "VS_PATH=%%i"
        set "VCVARS=%%i\VC\Auxiliary\Build\vcvars64.bat"
        if exist "!VCVARS!" (
            echo Found Visual Studio at: %%i
            call "!VCVARS!" >nul 2>&1
            goto :check_compiler
        )
    )
)

:: Fallback: Try common Visual Studio installation paths
echo Searching for Visual Studio in common locations...
set "VS_YEARS=2022 2019 2017"
set "VS_EDITIONS=Enterprise Professional Community BuildTools"
set "FOUND_VS="

for %%y in (%VS_YEARS%) do (
    for %%e in (%VS_EDITIONS%) do (
        set "VCVARS=C:\Program Files\Microsoft Visual Studio\%%y\%%e\VC\Auxiliary\Build\vcvars64.bat"
        if exist "!VCVARS!" (
            echo Found Visual Studio %%y %%e
            call "!VCVARS!" >nul 2>&1
            set "FOUND_VS=1"
            goto :check_compiler
        )

        :: Also check Program Files (x86)
        set "VCVARS=C:\Program Files (x86)\Microsoft Visual Studio\%%y\%%e\VC\Auxiliary\Build\vcvars64.bat"
        if exist "!VCVARS!" (
            echo Found Visual Studio %%y %%e (x86)
            call "!VCVARS!" >nul 2>&1
            set "FOUND_VS=1"
            goto :check_compiler
        )
    )
)

:: Try older Visual Studio versions (2015, 2013)
echo Checking for older Visual Studio versions...
if exist "C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\vcvarsall.bat" (
    echo Found Visual Studio 2015
    call "C:\Program Files (x86)\Microsoft Visual Studio 14.0\VC\vcvarsall.bat" x64 >nul 2>&1
    set "FOUND_VS=1"
    goto :check_compiler
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio 12.0\VC\vcvarsall.bat" (
    echo Found Visual Studio 2013
    call "C:\Program Files (x86)\Microsoft Visual Studio 12.0\VC\vcvarsall.bat" x64 >nul 2>&1
    set "FOUND_VS=1"
    goto :check_compiler
)

:check_compiler
:: Verify MSVC is now available
where cl >nul 2>&1
if not errorlevel 1 (
    echo MSVC compiler setup successful
    goto :compiler_ready
)

if not defined FOUND_VS (
    echo ERROR: Visual Studio not found or MSVC compiler not available
    echo.
    echo Please try one of the following:
    echo 1. Install Visual Studio 2019 or 2022 with C++ support
    echo 2. Run this script from a Visual Studio Developer Command Prompt
    echo 3. Run this script from a Visual Studio x64 Native Tools Command Prompt
    echo.
    echo You can also manually run the appropriate vcvars64.bat file before running this script
    pause
    exit /b 1
)

:compiler_ready

:: Final verification that MSVC is available
cl >nul 2>&1
if errorlevel 1 (
    echo ERROR: MSVC compiler still not available after setup
    echo.
    echo Troubleshooting steps:
    echo 1. Make sure Visual Studio is installed with "Desktop development with C++" workload
    echo 2. Try running from "x64 Native Tools Command Prompt for VS"
    echo 3. Manually run vcvars64.bat from your Visual Studio installation
    echo.
    echo Visual Studio installation paths checked:
    echo - C:\Program Files\Microsoft Visual Studio\2022\*
    echo - C:\Program Files\Microsoft Visual Studio\2019\*
    echo - C:\Program Files (x86)\Microsoft Visual Studio\*
    echo.
    pause
    exit /b 1
)

:: Check CMake
cmake --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: CMake not found
    echo Please install CMake and add it to PATH
    echo Download from: https://cmake.org/download/
    pause
    exit /b 1
)

:: Create and enter build directory
if not exist "build" mkdir build
cd build

:: Configure with CMake for Visual Studio
echo.
echo Configuring project for Visual Studio...
cmake .. -G "Visual Studio 17 2022" -A x64 -DCMAKE_BUILD_TYPE=Release
if errorlevel 1 (
    echo ERROR: CMake configuration failed
    cd ..
    pause
    exit /b 1
)

:: Build the project
echo.
echo Building project...
cmake --build . --config Release --parallel
if errorlevel 1 (
    echo ERROR: Build failed
    cd ..
    pause
    exit /b 1
)

:: Copy executables to root directory
echo.
echo Copying executables...
if exist "src\client\Release\hvnc_client.exe" (
    copy "src\client\Release\hvnc_client.exe" "..\hvnc_client.exe" >nul
    echo ✓ Client executable: hvnc_client.exe
)
if exist "src\server\Release\hvnc_server.exe" (
    copy "src\server\Release\hvnc_server.exe" "..\hvnc_server.exe" >nul
    echo ✓ Server executable: hvnc_server.exe
)

cd ..

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Executables are ready:
echo   hvnc_client.exe - Run on target machine
echo   hvnc_server.exe - Run on control machine
echo.
echo Configuration file: config.json
echo.
pause
