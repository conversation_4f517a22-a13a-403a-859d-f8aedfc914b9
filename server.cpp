// server.cpp - C++20 Screenshot Receiver
#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <iostream>
#include <fstream>
#include <vector>
#include <chrono>
#include <format>
#include <filesystem>

#pragma comment(lib, "Ws2_32.lib")

class ScreenshotServer {
private:
    SOCKET serverSocket;
    struct sockaddr_in serverAddr{};
    bool isRunning = false;

public:
    ScreenshotServer(int port = 8888) {
        WSADATA wsa;
        if (WSAStartup(MAKEWORD(2, 2), &wsa) != 0) {
            throw std::runtime_error("WSAStartup failed");
        }

        serverSocket = socket(AF_INET, SOCK_STREAM, 0);
        if (serverSocket == INVALID_SOCKET) {
            WSACleanup();
            throw std::runtime_error("Socket creation failed");
        }

        // Allow socket reuse
        int opt = 1;
        setsockopt(serverSocket, SOL_SOCKET, SO_REUSEADDR, (char*)&opt, sizeof(opt));

        serverAddr.sin_family = AF_INET;
        serverAddr.sin_addr.s_addr = INADDR_ANY;
        serverAddr.sin_port = htons(port);

        if (bind(serverSocket, (struct sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
            closesocket(serverSocket);
            WSACleanup();
            throw std::runtime_error("Bind failed");
        }

        if (listen(serverSocket, 5) == SOCKET_ERROR) {
            closesocket(serverSocket);
            WSACleanup();
            throw std::runtime_error("Listen failed");
        }

        std::cout << "[*] Server listening on port " << port << "\n";
        isRunning = true;
    }

    ~ScreenshotServer() {
        if (isRunning) {
            closesocket(serverSocket);
            WSACleanup();
        }
    }

    void Start() {
        std::filesystem::create_directory("screenshots");
        
        while (isRunning) {
            std::cout << "[*] Waiting for connection...\n";
            
            struct sockaddr_in clientAddr{};
            int clientSize = sizeof(clientAddr);
            SOCKET clientSocket = accept(serverSocket, (struct sockaddr*)&clientAddr, &clientSize);
            
            if (clientSocket == INVALID_SOCKET) {
                std::cerr << "[!] Accept failed\n";
                continue;
            }

            char clientIP[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &clientAddr.sin_addr, clientIP, INET_ADDRSTRLEN);
            std::cout << "[+] Client connected from " << clientIP << "\n";

            HandleClient(clientSocket);
            closesocket(clientSocket);
        }
    }

private:
    void HandleClient(SOCKET clientSocket) {
        try {
            // Receive image size first
            uint32_t imageSize = 0;
            int received = recv(clientSocket, (char*)&imageSize, sizeof(uint32_t), 0);
            if (received != sizeof(uint32_t)) {
                std::cerr << "[!] Failed to receive image size\n";
                return;
            }

            std::cout << "[*] Expecting " << imageSize << " bytes\n";

            // Receive image data
            std::vector<unsigned char> buffer(imageSize);
            size_t totalReceived = 0;
            
            while (totalReceived < imageSize) {
                int ret = recv(clientSocket, (char*)buffer.data() + totalReceived, 
                              imageSize - totalReceived, 0);
                if (ret <= 0) {
                    std::cerr << "[!] Connection lost during transfer\n";
                    return;
                }
                totalReceived += ret;
            }

            // Generate timestamp filename
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            auto tm = *std::localtime(&time_t);
            
            char filename[256];
            sprintf_s(filename, sizeof(filename), "screenshots/screenshot_%04d%02d%02d_%02d%02d%02d.png",
                tm.tm_year + 1900, tm.tm_mon + 1, tm.tm_mday,
                tm.tm_hour, tm.tm_min, tm.tm_sec);

            // Save image
            std::ofstream outFile(filename, std::ios::binary);
            if (!outFile) {
                std::cerr << "[!] Failed to create output file\n";
                return;
            }

            outFile.write((char*)buffer.data(), imageSize);
            outFile.close();

            std::cout << "[+] Screenshot saved as '" << filename << "'\n";

            // Send acknowledgment
            const char* ack = "OK";
            send(clientSocket, ack, 2, 0);

        } catch (const std::exception& e) {
            std::cerr << "[!] Error handling client: " << e.what() << "\n";
        }
    }
};

int main() {
    try {
        ScreenshotServer server(8888);
        server.Start();
    } catch (const std::exception& e) {
        std::cerr << "[!] Server error: " << e.what() << "\n";
        return 1;
    }

    return 0;
}
