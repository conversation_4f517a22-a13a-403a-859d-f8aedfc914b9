// hvnc_common.h - Common definitions and utilities for HVNC system
#pragma once

#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <iostream>
#include <vector>
#include <string>
#include <thread>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <chrono>
#include <memory>
#include <queue>

// Network protocol constants
#define HVNC_MAGIC_HEADER    0x48564E43  // "HVNC"
#define HVNC_VERSION         1
#define HVNC_MAX_PACKET_SIZE (50 * 1024 * 1024)  // 50MB

// Message types
enum HVNCMessageType : uint32_t {
    FRAME_DATA = 1,
    HEARTBEAT = 2,
    DISCONNECT = 3,
    RESOLUTION_CHANGE = 4
};

// Network packet structure
#pragma pack(push, 1)
struct HVNCPacketHeader {
    uint32_t magic = HVNC_MAGIC_HEADER;
    uint32_t version = HVNC_VERSION;
    HVNCMessageType messageType;
    uint32_t dataSize;
    uint32_t checksum;
    uint64_t timestamp;
};
#pragma pack(pop)

// Thread-safe queue for network packets
template<typename T>
class ThreadSafeQueue {
private:
    std::queue<T> queue;
    std::mutex mutex;
    std::condition_variable condition;

public:
    void Push(const T& item) {
        std::lock_guard<std::mutex> lock(mutex);
        queue.push(item);
        condition.notify_one();
    }

    bool Pop(T& item, int timeoutMs = -1) {
        std::unique_lock<std::mutex> lock(mutex);
        
        if (timeoutMs < 0) {
            condition.wait(lock, [this] { return !queue.empty(); });
        } else {
            if (!condition.wait_for(lock, std::chrono::milliseconds(timeoutMs),
                                   [this] { return !queue.empty(); })) {
                return false;
            }
        }
        
        item = queue.front();
        queue.pop();
        return true;
    }

    bool Empty() const {
        std::lock_guard<std::mutex> lock(mutex);
        return queue.empty();
    }

    size_t Size() const {
        std::lock_guard<std::mutex> lock(mutex);
        return queue.size();
    }

    void Clear() {
        std::lock_guard<std::mutex> lock(mutex);
        while (!queue.empty()) {
            queue.pop();
        }
    }
};

// Network packet wrapper
struct NetworkPacket {
    HVNCPacketHeader header;
    std::vector<uint8_t> data;
    
    NetworkPacket() = default;
    
    NetworkPacket(HVNCMessageType type, const std::vector<uint8_t>& payload) {
        header.messageType = type;
        header.dataSize = static_cast<uint32_t>(payload.size());
        header.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now().time_since_epoch()).count();
        header.checksum = CalculateChecksum(payload);
        data = payload;
    }

private:
    uint32_t CalculateChecksum(const std::vector<uint8_t>& data) {
        uint32_t checksum = 0;
        for (size_t i = 0; i < data.size(); i += 4) {
            uint32_t chunk = 0;
            for (int j = 0; j < 4 && i + j < data.size(); ++j) {
                chunk |= (data[i + j] << (j * 8));
            }
            checksum ^= chunk;
        }
        return checksum;
    }
};

// Connection state management
enum ConnectionState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    RECONNECTING,
    ERROR
};

class ConnectionManager {
private:
    std::atomic<ConnectionState> state;
    std::atomic<uint64_t> lastHeartbeat;
    std::atomic<uint64_t> reconnectAttempts;
    std::mutex stateMutex;

public:
    ConnectionManager() : state(ConnectionState::DISCONNECTED), 
                         lastHeartbeat(0), reconnectAttempts(0) {}

    void SetState(ConnectionState newState) {
        std::lock_guard<std::mutex> lock(stateMutex);
        state.store(newState);
        
        if (newState == ConnectionState::CONNECTED) {
            reconnectAttempts.store(0);
            UpdateHeartbeat();
        }
    }

    ConnectionState GetState() const {
        return state.load();
    }

    void UpdateHeartbeat() {
        lastHeartbeat.store(GetCurrentTimeMs());
    }

    bool IsHeartbeatExpired(uint64_t timeoutMs = 30000) const {
        uint64_t now = GetCurrentTimeMs();
        uint64_t lastBeat = lastHeartbeat.load();
        return (now - lastBeat) > timeoutMs;
    }

    void IncrementReconnectAttempts() {
        reconnectAttempts.fetch_add(1);
    }

    uint64_t GetReconnectAttempts() const {
        return reconnectAttempts.load();
    }

    uint64_t GetReconnectDelay() const {
        // Exponential backoff: 1s, 2s, 4s, 8s, max 30s
        uint64_t attempts = reconnectAttempts.load();
        uint64_t delay = std::min(1000ULL << attempts, 30000ULL);
        return delay;
    }

private:
    uint64_t GetCurrentTimeMs() const {
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now().time_since_epoch()).count();
    }
};

// Performance monitoring
class PerformanceMonitor {
private:
    std::atomic<uint64_t> framesProcessed;
    std::atomic<uint64_t> bytesTransferred;
    std::atomic<double> averageFPS;
    std::atomic<double> averageBandwidth;
    
    std::chrono::steady_clock::time_point startTime;
    std::chrono::steady_clock::time_point lastFrameTime;
    std::mutex perfMutex;

public:
    PerformanceMonitor() : framesProcessed(0), bytesTransferred(0), 
                          averageFPS(0.0), averageBandwidth(0.0) {
        Reset();
    }

    void Reset() {
        std::lock_guard<std::mutex> lock(perfMutex);
        startTime = std::chrono::steady_clock::now();
        lastFrameTime = startTime;
        framesProcessed.store(0);
        bytesTransferred.store(0);
        averageFPS.store(0.0);
        averageBandwidth.store(0.0);
    }

    void RecordFrame(size_t frameSize) {
        std::lock_guard<std::mutex> lock(perfMutex);
        
        auto now = std::chrono::steady_clock::now();
        framesProcessed.fetch_add(1);
        bytesTransferred.fetch_add(frameSize);
        
        // Calculate FPS
        auto totalTime = std::chrono::duration_cast<std::chrono::milliseconds>(
            now - startTime).count();
        if (totalTime > 0) {
            averageFPS.store(framesProcessed.load() * 1000.0 / totalTime);
        }
        
        // Calculate bandwidth (bytes per second)
        if (totalTime > 0) {
            averageBandwidth.store(bytesTransferred.load() * 1000.0 / totalTime);
        }
        
        lastFrameTime = now;
    }

    double GetFPS() const { return averageFPS.load(); }
    double GetBandwidth() const { return averageBandwidth.load(); }
    uint64_t GetFrameCount() const { return framesProcessed.load(); }
    uint64_t GetBytesTransferred() const { return bytesTransferred.load(); }
    
    uint64_t GetUptimeMs() const {
        auto now = std::chrono::steady_clock::now();
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            now - startTime).count();
    }
};

// Utility functions
class HVNCUtils {
public:
    static std::string GetTimestamp() {
        SYSTEMTIME st;
        GetLocalTime(&st);
        
        char buffer[64];
        sprintf_s(buffer, "%04d-%02d-%02d %02d:%02d:%02d.%03d",
                 st.wYear, st.wMonth, st.wDay,
                 st.wHour, st.wMinute, st.wSecond, st.wMilliseconds);
        return std::string(buffer);
    }

    static std::string FormatBytes(uint64_t bytes) {
        const char* units[] = { "B", "KB", "MB", "GB" };
        int unitIndex = 0;
        double size = static_cast<double>(bytes);
        
        while (size >= 1024.0 && unitIndex < 3) {
            size /= 1024.0;
            unitIndex++;
        }
        
        char buffer[32];
        sprintf_s(buffer, "%.1f %s", size, units[unitIndex]);
        return std::string(buffer);
    }

    static bool CreateDirectoryIfNotExists(const std::string& path) {
        DWORD attrs = GetFileAttributesA(path.c_str());
        if (attrs == INVALID_FILE_ATTRIBUTES) {
            return CreateDirectoryA(path.c_str(), nullptr) != 0;
        }
        return (attrs & FILE_ATTRIBUTE_DIRECTORY) != 0;
    }

    static void LogMessage(const std::string& level, const std::string& message) {
        std::cout << "[" << GetTimestamp() << "] [" << level << "] " << message << std::endl;
    }
};

// Logging macros
#define LOG_INFO(msg) HVNCUtils::LogMessage("INFO", msg)
#define LOG_WARN(msg) HVNCUtils::LogMessage("WARN", msg)
#define LOG_ERROR(msg) HVNCUtils::LogMessage("ERROR", msg)
#define LOG_DEBUG(msg) HVNCUtils::LogMessage("DEBUG", msg)
