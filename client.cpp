// client.cpp - C++20 Screenshot Sender
#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>

#define STB_IMAGE_WRITE_IMPLEMENTATION
#include "stb_image_write.h"
#include <iostream>
#include <vector>
#include <string>
#include <format>
#include <chrono>
#include <thread>

#pragma comment(lib, "Ws2_32.lib")

class ScreenCapture {
public:
    struct CaptureResult {
        std::vector<unsigned char> imageData;
        int width;
        int height;
        size_t originalSize;
    };

    static CaptureResult CaptureAndCompress(int quality = 90) {
        CaptureResult result{};
        
        // Get screen dimensions
        result.width = GetSystemMetrics(SM_CXSCREEN);
        result.height = GetSystemMetrics(SM_CYSCREEN);
        
        // Create device contexts
        HDC hScreen = GetDC(NULL);
        HDC hDC = CreateCompatibleDC(hScreen);
        
        // Create bitmap
        HBITMAP hBitmap = CreateCompatibleBitmap(hScreen, result.width, result.height);
        HGDIOBJ oldBitmap = SelectObject(hDC, hBitmap);
        
        // Capture screen
        BitBlt(hDC, 0, 0, result.width, result.height, hScreen, 0, 0, SRCCOPY);
        
        // Setup bitmap info
        BITMAPINFO bmi{};
        bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
        bmi.bmiHeader.biWidth = result.width;
        bmi.bmiHeader.biHeight = -result.height; // Top-down
        bmi.bmiHeader.biPlanes = 1;
        bmi.bmiHeader.biBitCount = 24; // RGB
        bmi.bmiHeader.biCompression = BI_RGB;
        
        // Get pixel data
        std::vector<unsigned char> pixels(result.width * result.height * 3);
        GetDIBits(hDC, hBitmap, 0, result.height, pixels.data(), &bmi, DIB_RGB_COLORS);
        
        result.originalSize = pixels.size();
        
        // Convert BGR to RGB (Windows uses BGR)
        for (size_t i = 0; i < pixels.size(); i += 3) {
            std::swap(pixels[i], pixels[i + 2]);
        }
        
        // Compress to PNG (simpler than JPEG for this demo)
        auto writeFunc = [](void* context, void* data, int size) {
            auto* buffer = static_cast<std::vector<unsigned char>*>(context);
            buffer->insert(buffer->end(), (unsigned char*)data, (unsigned char*)data + size);
        };

        stbi_write_png_to_func(writeFunc, &result.imageData,
                              result.width, result.height, 3, pixels.data(), result.width * 3);
        
        // Cleanup
        SelectObject(hDC, oldBitmap);
        DeleteObject(hBitmap);
        DeleteDC(hDC);
        ReleaseDC(NULL, hScreen);
        
        return result;
    }
};

class ScreenshotClient {
private:
    std::string serverIP;
    int serverPort;
    
public:
    ScreenshotClient(const std::string& ip, int port) : serverIP(ip), serverPort(port) {
        WSADATA wsa;
        if (WSAStartup(MAKEWORD(2, 2), &wsa) != 0) {
            throw std::runtime_error("WSAStartup failed");
        }
    }
    
    ~ScreenshotClient() {
        WSACleanup();
    }
    
    bool SendScreenshot(int quality = 90) {
        try {
            // Capture and compress screenshot
            std::cout << "[*] Capturing screenshot...\n";
            auto capture = ScreenCapture::CaptureAndCompress(quality);
            
            double compressionRatio = (double)capture.imageData.size() / capture.originalSize * 100.0;
            std::cout << "[*] Captured " << capture.width << "x" << capture.height
                      << " (Original: " << capture.originalSize << " bytes, Compressed: "
                      << capture.imageData.size() << " bytes, Ratio: " << compressionRatio << "%)\n";
            
            // Connect to server
            SOCKET sock = socket(AF_INET, SOCK_STREAM, 0);
            if (sock == INVALID_SOCKET) {
                std::cerr << "[!] Socket creation failed\n";
                return false;
            }
            
            struct sockaddr_in server{};
            server.sin_family = AF_INET;
            inet_pton(AF_INET, serverIP.c_str(), &server.sin_addr);
            server.sin_port = htons(serverPort);
            
            std::cout << "[*] Connecting to " << serverIP << ":" << serverPort << "...\n";
            if (connect(sock, (struct sockaddr*)&server, sizeof(server)) == SOCKET_ERROR) {
                std::cerr << "[!] Connection failed\n";
                closesocket(sock);
                return false;
            }
            
            std::cout << "[+] Connected to server\n";
            
            // Send image size first
            uint32_t dataSize = static_cast<uint32_t>(capture.imageData.size());
            if (send(sock, (char*)&dataSize, sizeof(uint32_t), 0) == SOCKET_ERROR) {
                std::cerr << "[!] Failed to send image size\n";
                closesocket(sock);
                return false;
            }

            // Send image data
            size_t totalSent = 0;
            while (totalSent < capture.imageData.size()) {
                int sent = send(sock, (char*)capture.imageData.data() + totalSent,
                               capture.imageData.size() - totalSent, 0);
                if (sent == SOCKET_ERROR) {
                    std::cerr << "[!] Failed to send image data\n";
                    closesocket(sock);
                    return false;
                }
                totalSent += sent;
            }
            
            // Wait for acknowledgment
            char ack[3] = {0};
            recv(sock, ack, 2, 0);
            
            closesocket(sock);
            std::cout << "[+] Screenshot sent successfully\n";
            return true;
            
        } catch (const std::exception& e) {
            std::cerr << "[!] Error: " << e.what() << "\n";
            return false;
        }
    }
};

int main(int argc, char* argv[]) {
    std::string serverIP = "127.0.0.1";
    int serverPort = 8888;
    int quality = 90;
    bool continuous = false;
    int interval = 5; // seconds
    
    // Parse command line arguments
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "--ip" && i + 1 < argc) {
            serverIP = argv[++i];
        } else if (arg == "--port" && i + 1 < argc) {
            serverPort = std::stoi(argv[++i]);
        } else if (arg == "--quality" && i + 1 < argc) {
            quality = std::stoi(argv[++i]);
        } else if (arg == "--continuous") {
            continuous = true;
        } else if (arg == "--interval" && i + 1 < argc) {
            interval = std::stoi(argv[++i]);
        } else if (arg == "--help") {
            std::cout << "Usage: client.exe [options]\n"
                      << "Options:\n"
                      << "  --ip <address>     Server IP address (default: 127.0.0.1)\n"
                      << "  --port <port>      Server port (default: 8888)\n"
                      << "  --quality <1-100>  JPEG quality (default: 90)\n"
                      << "  --continuous       Send screenshots continuously\n"
                      << "  --interval <sec>   Interval between screenshots (default: 5)\n"
                      << "  --help             Show this help\n";
            return 0;
        }
    }
    
    try {
        ScreenshotClient client(serverIP, serverPort);
        
        if (continuous) {
            std::cout << "[*] Starting continuous mode (interval: " << interval << "s)\n";
            while (true) {
                client.SendScreenshot(quality);
                std::this_thread::sleep_for(std::chrono::seconds(interval));
            }
        } else {
            client.SendScreenshot(quality);
        }
        
    } catch (const std::exception& e) {
        std::cerr << "[!] Client error: " << e.what() << "\n";
        return 1;
    }
    
    return 0;
}
