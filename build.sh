#!/bin/bash

echo "Building Screenshot Transfer System..."

# Check if g++ is available
if ! command -v g++ &> /dev/null; then
    echo "g++ compiler not found. Please install g++ or use CMake build method."
    exit 1
fi

# Create build directory
mkdir -p build
cd build

echo
echo "Building server..."
g++ -std=c++20 -Wall -Wextra -O2 -o server ../server.cpp
if [ $? -ne 0 ]; then
    echo "Failed to build server"
    cd ..
    exit 1
fi

echo
echo "Building client..."
g++ -std=c++20 -Wall -Wextra -O2 -o client ../client.cpp
if [ $? -ne 0 ]; then
    echo "Failed to build client"
    cd ..
    exit 1
fi

echo
echo "Build completed successfully!"
echo
echo "Executables created:"
echo "  - build/server"
echo "  - build/client"
echo
echo "To test:"
echo "  1. Run: ./build/server"
echo "  2. In another terminal: ./build/client"
echo

cd ..
