@echo off
echo Testing BMP Image Quality...
echo.

if not exist "build\verify_bmp.exe" (
    echo Error: verify_bmp.exe not found. Please run build.bat first.
    pause
    exit /b 1
)

if not exist "screenshots\screenshot_20250710_115518.bmp" (
    echo Error: BMP file not found. Please run the client first.
    pause
    exit /b 1
)

echo Analyzing BMP file...
build\verify_bmp.exe screenshots\screenshot_20250710_115518.bmp

echo.
echo File information:
dir screenshots\screenshot_20250710_115518.bmp

echo.
echo Test completed!
pause
