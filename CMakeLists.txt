cmake_minimum_required(VERSION 3.20)
project(ScreenshotTransfer)

# Set C++20 standard
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Add compiler-specific flags
if(MSVC)
    add_compile_options(/W4)
    add_compile_definitions(_WIN32_WINNT=0x0601)  # Windows 7+
else()
    add_compile_options(-Wall -Wextra -pedantic)
endif()

# Find required libraries
if(WIN32)
    # Windows-specific libraries
    set(PLATFORM_LIBS ws2_32 gdi32 user32)
else()
    # Linux/Unix libraries (if you want to extend later)
    find_package(PkgConfig REQUIRED)
    pkg_check_modules(X11 REQUIRED x11)
    set(PLATFORM_LIBS ${X11_LIBRARIES})
endif()

# Server executable
add_executable(server server.cpp)
target_link_libraries(server ${PLATFORM_LIBS})

# Client executable  
add_executable(client client.cpp)
target_link_libraries(client ${PLATFORM_LIBS})

# Copy stb_image_write.h to build directory for reference
configure_file(${CMAKE_SOURCE_DIR}/stb_image_write.h ${CMAKE_BINARY_DIR}/stb_image_write.h COPYONLY)

# Set output directories
set_target_properties(server client PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Installation
install(TARGETS server client
    RUNTIME DESTINATION bin
)

install(FILES README.md
    DESTINATION .
)
